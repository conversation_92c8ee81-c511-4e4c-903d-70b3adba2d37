import { Modal, Descriptions, Divider, Tag } from "antd";
import {
  CalendarOutlined,
  TeamOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
  FileOutlined,
} from "@ant-design/icons";
import { DA_NoiDungCuocHopType } from "@/types/dA_DuAn/dA_NoiDungCuocHop";

interface Props {
  item: DA_NoiDungCuocHopType;
  onClose: () => void;
}

const DA_NoiDungCuocHopDetail: React.FC<Props> = ({ item, onClose }) => {
  return (
    <Modal
      title={
        <div
          style={{ display: "flex", alignItems: "center", gap: 8, margin: 10 }}
        >
          <FileTextOutlined style={{ fontSize: 20, color: "#1890ff" }} />
          <span>Chi tiết cuộc họp</span>
        </div>
      }
      open={true}
      onCancel={onClose}
      footer={null}
      width={800}
      centered // Hiển thị ở giữa màn hình
      bodyStyle={{ padding: "24px 24px 0" }}
    >
      <Descriptions bordered column={1}>
        <Descriptions.Item label={<strong>Tên dự án</strong>}>
          {item.tenDuAn}
        </Descriptions.Item>

        <Descriptions.Item label={<strong>Loại cuộc họp</strong>}>
          <Tag color={item.isNoiBo ? "blue" : "green"}>
            {item.isNoiBo ? "Nội bộ" : "Với khách hàng"}
          </Tag>
        </Descriptions.Item>

        <Descriptions.Item
          label={
            <strong>
              <CalendarOutlined /> Thời gian
            </strong>
          }
        >
          {new Date(item.thoiGianHop).toLocaleString()}
        </Descriptions.Item>

        <Descriptions.Item
          label={
            <strong>
              <TeamOutlined /> Thành phần tham gia
            </strong>
          }
        >
          {item.thanhPhanThamGia}
        </Descriptions.Item>

        <Descriptions.Item
          label={
            <strong>
              <EnvironmentOutlined /> Địa điểm
            </strong>
          }
        >
          {item.diaDiemCuocHop}
        </Descriptions.Item>

        <Descriptions.Item
          label={
            <strong>
              <FileOutlined /> Tài liệu đính kèm ({item.soTaiLieu})
            </strong>
          }
        >
          {item.listTaiLieu?.map((file) => (
            <div key={file.id}>
              <a
                href={`${process.env.NEXT_PUBLIC_STATIC_FILE_BASE_URL}/${file.duongDanFile}`}
                target="_blank"
              >
                {file.tenTaiLieu}
              </a>
            </div>
          ))}
        </Descriptions.Item>
      </Descriptions>

      <Divider orientation="left" style={{ margin: "24px 0 16px" }}>
        Nội dung cuộc họp
      </Divider>

      <div
        dangerouslySetInnerHTML={{ __html: item.noiDungCuocHop }}
        style={{
          border: "1px solid #f0f0f0",
          borderRadius: 4,
          padding: 16,
          minHeight: 200,
          maxHeight: 400,
          overflowY: "auto",
        }}
      />
    </Modal>
  );
};

export default DA_NoiDungCuocHopDetail;
