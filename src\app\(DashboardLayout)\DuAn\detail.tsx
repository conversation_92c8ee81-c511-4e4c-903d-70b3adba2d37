"use client";
import React from "react";
import {
	Row,
	Col,
	Card,
	Descriptions,
	Button,
	Drawer,
	Table,
	Modal,
	Dropdown,
	Menu,
} from "antd";
import dayjs from "dayjs";
import { EditOutlined, DownOutlined, UploadOutlined, SaveOutlined, DeleteOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { DA_DuAnType } from "@/types/dA_DuAn/dA_DuAn.d";
import { DA_KeHoachThucHienCreateOrUpdateType } from "@/types/dA_DuAn/dA_KeHoachThucHien";
import dA_KeHoachThucHienService from "@/services/dA_KeHoachThucHien/dA_KeHoachThucHienService";
import KeHoachTrienKhaiCustomTable from "./KeHoachTrienKhai";
import { useRouter } from "next/navigation";
import AutoBreadcrumb from "@/components/util-compenents/Breadcrumb";
import CustomBreadcrumb from "@/components/util-compenents/CustomBreadcrumb";
import DetailKeHoachTrienKhai from "./DetailKeHoachTrienKhai";
import CommonUpload from '@/components/shared-components/CommonUpload';
import { toast } from "react-toastify";
import TaiLieuDinhKemService from "@/services/taiLieuDinhKem/taiLieuDinhKem.service";
import FileTypeConstant from "@/constants/FileTypeConstant";
import UploadedFilesModal from "@/components/shared-components/UploadedFilesModal";
import { TaiLieuDinhKem } from "@/types/taiLieuDinhKem/taiLieuDinhKem";
import DA_NoiDungCuocHopPage from "../dA_NoiDungCuocHop/page";
import PhanCongTable from "./PhanCongTable";
import { DropdownOption } from "@/types/general";
import { userService } from "@/services/user/user.service";
import { roleService } from "@/services/role/role.service";
import dA_DuAnService from "@/services/dA_DuAn/dA_DuAnService";
import dA_PhanCongService from "@/services/dA_DuAn/dA_PhanCongService";
import { DA_PhanCongCreateOrUpdateType } from "@/types/dA_DuAn/dA_PhanCong";
import { duLieuDanhMucService } from "@/services/duLieuDanhMuc/duLieuDanhMuc.service";
interface Props {
	item: DA_DuAnType;
	itemId: string | null;
	onClose?: () => void;
	onRefresh?: () => void; // Callback để refresh dữ liệu từ component cha
}

const formatDate = (date: any) =>
	date ? dayjs(date).format("DD/MM/YYYY") : "";

const DA_DuAnDetailView: React.FC<Props> = ({ item, onRefresh }) => {
	const router = useRouter();
	const [currentItemKeHoach, setCurrentItemKeHoach] = React.useState<DA_KeHoachThucHienCreateOrUpdateType[] | null>(null);
	const [isKeHoachNoiBo] = React.useState<boolean>(true);
	const [isEditModalOpen, setIsEditModalOpen] = React.useState(false);
	const [isDetailKeHoachModalOpen, SetDetailKeHoachModalOpen] = React.useState(false);
	const [isUploadModalOpen, setIsUploadModalOpen] = React.useState(false);
	const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
	const [uploadingFile, setUploadingFile] = React.useState(false);
	const [isUploadedFilesModalOpen, setIsUploadedFilesModalOpen] = React.useState(false);
	const [uploadedFiles, setUploadedFiles] = React.useState<TaiLieuDinhKem[]>([]);
	const [isPhanCongModalOpen, setIsPhanCongModalOpen] = React.useState(false);
	const [userOptions, setUserOptions] = React.useState<DropdownOption[]>([]);
	const [roleOptions, setRoleOptions] = React.useState<DropdownOption[]>([]);
	const [phanCongList, setPhanCongList] = React.useState<DA_PhanCongCreateOrUpdateType[]>([]);
	// State riêng cho hiển thị danh sách phân công trên chi tiết dự án
	const [phanCongDisplayList, setPhanCongDisplayList] = React.useState<any[]>([]);

	// Function đóng modal phân công
	const handleClosePhanCongModal = () => {
		setIsPhanCongModalOpen(false);
		// Reset lại state để đảm bảo dữ liệu fresh khi mở lại
		setPhanCongList([]);
		setUserOptions([]);
		setRoleOptions([]);
		console.log("Closed phân công modal and reset state");
	};

	// Function load danh sách phân công hiển thị trên chi tiết dự án
	const loadPhanCongDisplayList = async () => {
		if (!item?.id) return;
		
		try {
			const response = await dA_PhanCongService.GetListByDuAnIdDto(item.id, []);
			if (response?.status && response.data) {
				setPhanCongDisplayList(response.data);
				console.log("Loaded phân công display list:", response.data);
			} else {
				console.log("No phân công display data returned");
				setPhanCongDisplayList([]);
			}
		} catch (error) {
			console.error("Error loading phân công display list:", error);
			// Fallback về dữ liệu từ props nếu có lỗi
			setPhanCongDisplayList(item?.phanCongList || []);
		}
	};

	React.useEffect(() => {
		// Gọi API lấy chi tiết kế hoạch nội bộ khi vào trang detail
		const fetchData = async () => {
			if (item?.id) {
				const res = await dA_KeHoachThucHienService.getFormByDuAn(
					item.id,
					true
				);
				const list = Array.isArray(res.data?.keHoachThucHienList)
					? res.data.keHoachThucHienList
					: [];
				setCurrentItemKeHoach(list);
			}
		};
		fetchData();

		// Load danh sách phân công hiển thị khi component khởi tạo
		loadPhanCongDisplayList();

		// Khởi tạo phanCongDisplayList từ props làm fallback
		if (item?.phanCongList && Array.isArray(item.phanCongList)) {
			setPhanCongDisplayList(item.phanCongList);
		}

		// Khởi tạo phanCongList từ item (chỉ khi modal chưa mở)
		if (!isPhanCongModalOpen && item?.phanCongList && Array.isArray(item.phanCongList)) {
			// Convert từ DA_PhanCongType sang DA_PhanCongCreateOrUpdateType
			const convertedList: DA_PhanCongCreateOrUpdateType[] = item.phanCongList.map(pc => ({
				id: pc.id,
				duAnId: item.id,
				userId: pc.userId || "",
				vaiTroId: pc.vaiTroId || null,
			}));
			setPhanCongList(convertedList);
		} else if (!isPhanCongModalOpen) {
			setPhanCongList([]);
		}
	}, [item?.id, item?.phanCongList, isPhanCongModalOpen]);

	// Load user, role options và danh sách phân công mới nhất khi mở modal phân công
	React.useEffect(() => {
		const fetchDataForModal = async () => {
			if (isPhanCongModalOpen && item?.id) {
				try {
					const [userRes, roleRes] = await Promise.all([
						userService.getDropdown(),
						duLieuDanhMucService.GetDropdownByGroupCode("VTDA")
					]);
					
					if (userRes?.status && userRes.data) {
						setUserOptions(userRes.data);
					}
					if (roleRes?.status && roleRes.data) {
						setRoleOptions(roleRes.data);
					}
					
					// Lấy danh sách phân công mới nhất từ backend
					try {
						const phanCongRes = await dA_PhanCongService.GetListByDuAnId(item.id, []);
						if (phanCongRes?.status && phanCongRes.data) {
							const convertedList: DA_PhanCongCreateOrUpdateType[] = phanCongRes.data.map((pc: any) => ({
								userId: pc.UserId || pc.userId || "",
								vaiTroId: pc.VaiTroId || pc.vaiTroId || "",
								tenVaiTro: pc.TenVaiTro || pc.tenVaiTro || "",
								tenNguoiDung: pc.TenNguoiDung || pc.tenNguoiDung || "",
								ghiChu: pc.GhiChu || pc.ghiChu || ""
							}));
							setPhanCongList(convertedList);
							console.log("Loaded latest phân công list from backend:", convertedList);
						} else {
							console.log("No phân công data returned from backend");
						}
					} catch (phanCongError) {
						console.error("Error fetching phân công list:", phanCongError);
						// Nếu có lỗi, giữ nguyên dữ liệu từ props
					}
				} catch (error) {
					console.error("Error fetching dropdowns for modal:", error);
				}
			}
		};
		fetchDataForModal();
	}, [isPhanCongModalOpen, item?.id]);

	// Menu thao tác riêng cho từng loại file
	const menuKhaoSat = (
		<Menu>
			<Menu.Item key="upload">Tải lên</Menu.Item>
			<Menu.Item key="view">Xem</Menu.Item>
		</Menu>
	);
	const menuNoiDungKhaoSat = (
		<Menu>
			<Menu.Item key="upload">Tải lên</Menu.Item>
			<Menu.Item key="view">Xem</Menu.Item>
			<Menu.Item key="delete" danger>
				Xóa
			</Menu.Item>
		</Menu>
	);
	const menuKeHoachTrienKhaiKH = (
		<Menu>
			<Menu.Item key="upload">Tải lên</Menu.Item>
			<Menu.Item key="view">Xem</Menu.Item>
			<Menu.Item key="history">Xem lịch sử</Menu.Item>
		</Menu>
	);
	const menuKeHoachTrienKhaiNoiBo = (
		<Menu>
			<Menu.Item
				key="upload"
				icon={<UploadOutlined />}
				onClick={() => setIsUploadModalOpen(true)}
			>
				Tải lên
			</Menu.Item>
			<Menu.Item
				key="edit"
				onClick={() => setIsEditModalOpen(true)}
				icon={<EditOutlined />}
			>
				Cập nhật
			</Menu.Item>
			<Menu.Item
				key="view"
				icon={<ExclamationCircleOutlined />}
				onClick={() => SetDetailKeHoachModalOpen(true)}
			>
				Xem kế hoạch
			</Menu.Item>
			<Menu.Divider />
			<Menu.Item
				key="Delete"
				onClick={() => deleteKeHoachTrienKhai(item?.id, true)}
				icon={<DeleteOutlined />}
				danger
			>
				Xoá
			</Menu.Item>
		</Menu>
	);
	const menuTestCase = (
		<Menu>
			<Menu.Item key="upload">Tải lên</Menu.Item>
			<Menu.Item key="view">Xem</Menu.Item>
		</Menu>
	);
	const menuChecklist = (
		<Menu>
			<Menu.Item key="upload">Tải lên</Menu.Item>
			<Menu.Item key="view">Xem</Menu.Item>
		</Menu>
	);
	const menuNghiemThu = (
		<Menu>
			<Menu.Item key="upload">Tải lên</Menu.Item>
			<Menu.Item key="view">Xem</Menu.Item>
		</Menu>
	);


	const deleteKeHoachTrienKhai = async (
		id: string,
		isNoiBo: boolean
	) => {
		Modal.confirm({
			title: "Bạn có chắc chắn muốn xoá kế hoạch này?",
			content: "Thao tác này không thể hoàn tác.",
			okText: "Xoá",
			okType: "danger",
			cancelText: "Huỷ",
			onOk: async () => {
				try {
					const res = await dA_KeHoachThucHienService.deleteByDuAnNew(id,isNoiBo);
					if (res.status) {
						toast.success("Xoá kế hoạch triển khai thành công!");
						// Cập nhật lại danh sách kế hoạch nếu cần
						// if (item?.id) {
						// 	const reload = await dA_KeHoachThucHienService.getFormByDuAn(item.id, isNoiBo);
						// 	const list = Array.isArray(reload.data?.keHoachThucHienList) ? reload.data.keHoachThucHienList : [];
						// 	setCurrentItemKeHoach(list);
						// }
					} else {
						toast.error(res.message || "Xoá thất bại!");
					}
				} catch (err) {
						console.log(err);
					toast.error("Xoá thất bại!");
				}
			},
		});
	};


	const handleUploadFile = async () => {
		if (!selectedFile) return;
		setUploadingFile(true);
		try {
			// TODO: Gọi API upload file ở đây
			// const formData = new FormData();
			// formData.append('file', selectedFile);
			// await api.uploadFile(formData);
			setIsUploadModalOpen(false);
			setSelectedFile(null);
			// Hiển thị thông báo thành công nếu cần
		} catch (err) {
			// Hiển thị thông báo lỗi nếu cần
		} finally {
			setUploadingFile(false);
		}
	};


	const handleUpload = async (files: File[], extraData?: Record<string, any>) => {
		if (!files || files.length === 0) return;
		setUploadingFile(true);
		try {
			// Gọi API upload với list file và extraData
			const response = await TaiLieuDinhKemService.uploadMulti(files, extraData);
			if (response.status) {
				toast.success("Tải lên thành công!");
			} else {
				toast.error("Tải lên thất bại!");
			}
			setIsUploadModalOpen(false);
			setSelectedFile(null);
		} catch (err) {
			toast.error("Tải lên thất bại!");
		} finally {
			setUploadingFile(false);
		}
	};

	const fetchUploadedFiles = React.useCallback(async () => {
		if (!item.id) return;
		const res = await TaiLieuDinhKemService.getByItemIdAndLoaiTaiLieu(item.id, FileTypeConstant.DA_KeHoachNoiBo);
		if (res?.status && Array.isArray(res.data)) {
			setUploadedFiles(res.data);
		} else {
			setUploadedFiles([]);
		}
	}, [item.id]);

	React.useEffect(() => {
		if (isUploadedFilesModalOpen) {
			fetchUploadedFiles();
		}
	}, [isUploadedFilesModalOpen, fetchUploadedFiles]);

	const handleDeleteFile = async (fileId: string) => {
		await TaiLieuDinhKemService.delete(fileId);
		fetchUploadedFiles();
	};

	// Handlers cho PhanCongTable
	const handleAddPhanCong = () => {
		setPhanCongList([...phanCongList, { 
			duAnId: item.id,
			userId: "", 
			vaiTroId: null 
		}]);
	};

	const handlePhanCongChange = (index: number, key: "userId" | "vaiTroId", value: string) => {
		const newList = [...phanCongList];
		newList[index] = { ...newList[index], [key]: value };
		setPhanCongList(newList);
	};

	const handleDeletePhanCong = (index: number) => {
		const newList = phanCongList.filter((_, i) => i !== index);
		setPhanCongList(newList);
	};

	const handleSavePhanCong = async () => {
		try {
			// Tạo payload với type DA_PhanCongCreateOrUpdateType[]
			const payload: DA_PhanCongCreateOrUpdateType[] = phanCongList
				.filter(pc => pc.userId) // Chỉ lấy những item có userId
				.map(pc => ({
					duAnId: item.id,
					userId: pc.userId,
					vaiTroId: pc.vaiTroId || null,
				}));
			
			console.log("Saving phan cong payload:", payload);
			
			// Gọi API để lưu phân công với duAnId và list
			const response = await dA_PhanCongService.saveValidList(item.id, payload);
			
			if (response.status) {
				toast.success("Lưu phân công thành công!");
				handleClosePhanCongModal();
				
				// Load lại danh sách phân công hiển thị trên chi tiết dự án
				await loadPhanCongDisplayList();
				
				// Gọi callback để refresh dữ liệu từ component cha nếu có
				if (onRefresh) {
					onRefresh();
				}
			} else {
				toast.error(response.message || "Lưu phân công thất bại!");
			}
		} catch (error) {
			console.error("Error saving phan cong:", error);
			toast.error("Có lỗi xảy ra khi lưu phân công!");
		}
	};

	return (
		<div>
			<CustomBreadcrumb
				items={[
					{ label: 'Trang chủ', href: '/dashboard' },
					{ label: 'Dự án', href: '/DuAn' },
					{ label: 'Chi tiết dự án' } // Không có href sẽ là item cuối, tự động bôi đậm
				]}
			/>
			<div style={{ padding: 0, background: '#fff' }}>

				<Card title="Thông tin dự án" bordered={false} style={{ marginBottom: 24 }}>
					<Descriptions
						column={2}
						labelStyle={{ color: "rgb(69, 85, 96)" }}
						contentStyle={{ minWidth: 180 }}
						size="middle"
					>
						<Descriptions.Item label="Tên dự án">{item.tenDuAn}</Descriptions.Item>
						<Descriptions.Item label="Trạng thái">{item.trangThaiThucHien}</Descriptions.Item>
						<Descriptions.Item label="Thời gian thực hiện">
							{item.ngayBatDau && item.ngayKetThuc
								? `${formatDate(item.ngayBatDau)} - ${formatDate(item.ngayKetThuc)}`
								: ""}
						</Descriptions.Item>
						<Descriptions.Item label="Ngày tiếp nhận">{formatDate(item.ngayTiepNhan)}</Descriptions.Item>
						<Descriptions.Item label="Thời gian cài đặt máy chủ">{formatDate(item.timeCaiDatMayChu)}</Descriptions.Item>
						<Descriptions.Item label="Backup máy chủ">
							{item.isBackupMayChu ? (
								<span style={{ color: "#52c41a", fontWeight: 600 }}>✔️</span>
							) : (
								<span style={{ color: "#ff4d4f", fontWeight: 500, display: "inline-flex", alignItems: "center" }}>
									<Button
										size="small"
										type="default"
										style={{
											marginRight: 6,
											padding: "0 6px",
											fontSize: 13,
											height: 22,
											borderColor: "#d9d9d9",
											background: "#fff",
											lineHeight: "20px",
											display: "inline-flex",
											alignItems: "center",
										}}
									>Chưa có</Button>
								</span>
							)}
						</Descriptions.Item>
						<Descriptions.Item label="Link demo">{item.linkDemo}</Descriptions.Item>
						<Descriptions.Item label="Link thực tế">{item.linkThucTe}</Descriptions.Item>
						<Descriptions.Item label="Yêu cầu dự án">
							<span
								style={{ minHeight: 24, display: "inline-block", maxWidth: 350, whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}
								dangerouslySetInnerHTML={{ __html: item.yeuCauDuAn || "" }}
							/>
						</Descriptions.Item>
						<Descriptions.Item label="Mô tả dự án">
							<span
								style={{ minHeight: 24, display: "inline-block", maxWidth: 350, whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}
								dangerouslySetInnerHTML={{ __html: item.moTaDuAn || "" }}
							/>
						</Descriptions.Item>
					</Descriptions>
				</Card>

				<Card title="Thông tin file" bordered={false} style={{ marginBottom: 24 }}>
					<Descriptions column={2} bordered={false}>
						<Descriptions.Item label="File câu hỏi khảo sát">
							{item.hasFileKhaoSat ? (
								<Button
									size="small"
									type="link"
									icon={<SaveOutlined />}
									style={{
										width: 32,
										height: 22,
										padding: 0,
										minWidth: 0,
										background: "#52c41a",
										borderColor: "#52c41a",
										display: "inline-flex",
										alignItems: "center",
										justifyContent: "center"
									}}
								/>
							) : (
								<span style={{ color: "#ff4d4f", fontWeight: 500, display: "inline-flex", alignItems: "center" }}>
									<Button
										size="small"
										type="default"
										style={{
											marginRight: 6,
											padding: "0 6px",
											fontSize: 13,
											height: 22,
											borderColor: "#ff4d4f",
											color: "#ff4d4f",
											background: "#fff0f0",
											lineHeight: "20px",
											display: "inline-flex",
											alignItems: "center",
										}}
									>Chưa có</Button>
									<Dropdown overlay={menuKhaoSat} trigger={["click"]}>
										<Button size="small" style={{ height: 22, fontSize: 13, padding: "0 6px", marginLeft: 4, display: 'inline-flex', alignItems: 'center' }}>
											Thao tác <DownOutlined style={{ fontSize: 13, marginLeft: 4 }} />
										</Button>
									</Dropdown>
								</span>
							)}
						</Descriptions.Item>
						<Descriptions.Item label="File nội dung khảo sát">
							{item.hasFileNoiDungKhaoSat ? (
								<Button
									size="small"
									type="primary"
									icon={<SaveOutlined />}
									style={{
										width: 32,
										height: 22,
										padding: 0,
										minWidth: 0,
										background: "#52c41a",
										borderColor: "#52c41a",
										display: "inline-flex",
										alignItems: "center",
										justifyContent: "center"
									}}
								/>
							) : (
								<span style={{ color: "#ff4d4f", fontWeight: 500, display: "inline-flex", alignItems: "center" }}>
									<Button
										size="small"
										type="default"
										style={{
											marginRight: 6,
											padding: "0 6px",
											fontSize: 13,
											height: 22,
											borderColor: "#ff4d4f",
											color: "#ff4d4f",
											background: "#fff0f0",
											lineHeight: "20px",
											display: "inline-flex",
											alignItems: "center",
										}}
									>Chưa có</Button>
									<Dropdown overlay={menuNoiDungKhaoSat} trigger={["click"]}>
										<Button size="small" style={{ height: 22, fontSize: 13, padding: "0 6px", marginLeft: 4, display: 'inline-flex', alignItems: 'center' }}>
											Thao tác <DownOutlined style={{ fontSize: 13, marginLeft: 4 }} />
										</Button>
									</Dropdown>
								</span>
							)}
						</Descriptions.Item>
						<Descriptions.Item label="Kế hoạch triển khai khách hàng">
							{item.hasFileKeHoachTrienKhaiKhachHang ? (
								<Button
									size="small"
									type="primary"
									icon={<SaveOutlined />}
									style={{
										width: 32,
										height: 22,
										padding: 0,
										minWidth: 0,
										background: "#52c41a",
										borderColor: "#52c41a",
										display: "inline-flex",
										alignItems: "center",
										justifyContent: "center"
									}}
								/>
							) : (
								<span style={{ color: "#ff4d4f", fontWeight: 500, display: "inline-flex", alignItems: "center" }}>
									<Button
										size="small"
										type="default"
										style={{
											marginRight: 6,
											padding: "0 6px",
											fontSize: 13,
											height: 22,
											borderColor: "#ff4d4f",
											color: "#ff4d4f",
											background: "#fff0f0",
											lineHeight: "20px",
											display: "inline-flex",
											alignItems: "center",
										}}
									>Chưa có</Button>
									<Dropdown overlay={menuKeHoachTrienKhaiKH} trigger={["click"]}>
										<Button size="small" style={{ height: 22, fontSize: 13, padding: "0 6px", marginLeft: 4, display: 'inline-flex', alignItems: 'center' }}>
											Thao tác <DownOutlined style={{ fontSize: 13, marginLeft: 4 }} />
										</Button>
									</Dropdown>
								</span>
							)}
						</Descriptions.Item>
						<Descriptions.Item label="Kế hoạch triển khai nội bộ">
							{item.hasFileKeHoachTrienKhaiNoiBo ? (
								<Button
									size="small"
									type="default"
									icon={<ExclamationCircleOutlined style={{ color: '#52c41a', fontSize: 16 }} />}
									style={{
										width: 32,
										height: 22,
										padding: 0,
										minWidth: 0,
										background: "#f6ffed",
										borderColor: "#b7eb8f",
										color: "#52c41a",
										display: "inline-flex",
										alignItems: "center",
										justifyContent: "center"
									}}
									onClick={() => setIsUploadedFilesModalOpen(true)}
								/>
							) : (
								<span style={{ color: "#ff4d4f", fontWeight: 500, display: "inline-flex", alignItems: "center" }}>
									<Button
										size="small"
										type="default"
										style={{
											marginRight: 6,
											padding: "0 6px",
											fontSize: 13,
											height: 22,
											borderColor: "#ff4d4f",
											color: "#ff4d4f",
											background: "#fff0f0",
											lineHeight: "20px",
											display: "inline-flex",
											alignItems: "center",
										}}
									>Chưa có</Button>
								</span>
							)}
							<Dropdown overlay={menuKeHoachTrienKhaiNoiBo} trigger={["click"]}>
								<Button size="small" style={{ height: 22, fontSize: 13, padding: "0 6px", marginLeft: 4, display: 'inline-flex', alignItems: 'center' }}>
									Thao tác <DownOutlined style={{ fontSize: 13, marginLeft: 4 }} />
								</Button>
							</Dropdown>
						</Descriptions.Item>
						<Descriptions.Item label="Test case">
							{item.hasFileTestCase ? (
								<Button
									size="small"
									type="primary"
									icon={<SaveOutlined />}
									style={{
										width: 32,
										height: 22,
										padding: 0,
										minWidth: 0,
										background: "#52c41a",
										borderColor: "#52c41a",
										display: "inline-flex",
										alignItems: "center",
										justifyContent: "center"
									}}
								/>
							) : (
								<span style={{ color: "#ff4d4f", fontWeight: 500, display: "inline-flex", alignItems: "center" }}>
									<Button
										size="small"
										type="default"
										style={{
											marginRight: 6,
											padding: "0 6px",
											fontSize: 13,
											height: 22,
											borderColor: "#ff4d4f",
											color: "#ff4d4f",
											background: "#fff0f0",
											lineHeight: "20px",
											display: "inline-flex",
											alignItems: "center",
										}}
									>Chưa có</Button>
									<Dropdown overlay={menuTestCase} trigger={["click"]}>
										<Button size="small" style={{ height: 22, fontSize: 13, padding: "0 6px", marginLeft: 4, display: 'inline-flex', alignItems: 'center' }}>
											Thao tác <DownOutlined style={{ fontSize: 13, marginLeft: 4 }} />
										</Button>
									</Dropdown>
								</span>
							)}
						</Descriptions.Item>
						<Descriptions.Item label="Checklist nghiệm thu kỹ thuật">
							{item.hasCheckListNghiemThuKyThuat ? (
								<Button
									size="small"
									type="primary"
									icon={<SaveOutlined />}
									style={{
										width: 32,
										height: 22,
										padding: 0,
										minWidth: 0,
										background: "#52c41a",
										borderColor: "#52c41a",
										display: "inline-flex",
										alignItems: "center",
										justifyContent: "center"
									}}
								/>
							) : (
								<span style={{ color: "#ff4d4f", fontWeight: 500, display: "inline-flex", alignItems: "center" }}>
									<Button
										size="small"
										type="default"
										style={{
											marginRight: 6,
											padding: "0 6px",
											fontSize: 13,
											height: 22,
											borderColor: "#ff4d4f",
											color: "#ff4d4f",
											background: "#fff0f0",
											lineHeight: "20px",
											display: "inline-flex",
											alignItems: "center",
										}}
									>Chưa có</Button>
									<Dropdown overlay={menuChecklist} trigger={["click"]}>
										<Button size="small" style={{ height: 22, fontSize: 13, padding: "0 6px", marginLeft: 4, display: 'inline-flex', alignItems: 'center' }}>
											Thao tác <DownOutlined style={{ fontSize: 13, marginLeft: 4 }} />
										</Button>
									</Dropdown>
								</span>
							)}
						</Descriptions.Item>
						<Descriptions.Item label="File nghiệm thu kỹ thuật">
							{item.hasFileNghiemThuKyThuat ? (
								<Button
									size="small"
									type="primary"
									icon={<SaveOutlined />}
									style={{
										width: 32,
										height: 22,
										padding: 0,
										minWidth: 0,
										background: "#52c41a",
										borderColor: "#52c41a",
										display: "inline-flex",
										alignItems: "center",
										justifyContent: "center"
									}}
								/>
							) : (
								<span style={{ color: "#ff4d4f", fontWeight: 500, display: "inline-flex", alignItems: "center" }}>
									<Button
										size="small"
										type="default"
										style={{
											marginRight: 6,
											padding: "0 6px",
											fontSize: 13,
											height: 22,
											borderColor: "#ff4d4f",
											color: "#ff4d4f",
											background: "#fff0f0",
											lineHeight: "20px",
											display: "inline-flex",
											alignItems: "center",
										}}
									>Chưa có</Button>
									<Dropdown overlay={menuNghiemThu} trigger={["click"]}>
										<Button size="small" style={{ height: 22, fontSize: 13, padding: "0 6px", marginLeft: 4, display: 'inline-flex', alignItems: 'center' }}>
											Thao tác <DownOutlined style={{ fontSize: 13, marginLeft: 4 }} />
										</Button>
									</Dropdown>
								</span>
							)}
						</Descriptions.Item>
					</Descriptions>
				</Card>

				<Card 
					title="Thành viên dự án" 
					bordered={false} 
					style={{ marginBottom: 24 }}
					extra={
						<Button 
							type="primary" 
							icon={<EditOutlined />}
							onClick={() => setIsPhanCongModalOpen(true)}
						>
						</Button>
					}
				>
					{phanCongDisplayList && phanCongDisplayList.length > 0 ? (
						<Table
							style={{ marginTop: 24 }}
							size="small"
							bordered
							pagination={false}
							dataSource={phanCongDisplayList.map((pc, idx) => ({ ...pc, key: idx }))}
							columns={[
								{ title: "STT", dataIndex: "stt", key: "stt", width: 60, render: (_: any, __: any, i: number) => i + 1 },
								{ title: "Thành viên", dataIndex: "tenUser", key: "tenUser" },
								{ title: "Vai trò", dataIndex: "tenVaiTro", key: "tenVaiTro" },
							]}
						/>
					) : (
						<div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
							Chưa có thành viên nào được phân công
						</div>
					)}
				</Card>
				{isEditModalOpen && (
					<Modal
						title="Chỉnh sửa file KH triển khai nội bộ"
						open={isEditModalOpen}
						onCancel={() => setIsEditModalOpen(false)}
						footer={null}
					>
						<KeHoachTrienKhaiCustomTable item={currentItemKeHoach} onClose={() => setIsEditModalOpen(false)} iskeHoachNoiBo={isKeHoachNoiBo} idDuAn={item.id} />
					</Modal>
				)}
				{isDetailKeHoachModalOpen && (
					<Drawer
						title="Chi tiết kế hoạch triển khai nội bộ"
						open={isDetailKeHoachModalOpen}
						onClose={() => SetDetailKeHoachModalOpen(false)}
						width={900}
						destroyOnClose
						placement="right"
					>
						<DetailKeHoachTrienKhai item={currentItemKeHoach} idDuAn={item.id} />
					</Drawer>
				)}


				{isUploadModalOpen && (

					<Modal
						title="Tải lên file kế hoạch triển khai nội bộ"
						open={isUploadModalOpen}
						onCancel={() => setIsUploadModalOpen(false)}
						footer={null}
						width={420}
						bodyStyle={{ padding: 32, paddingTop: 16 }}
					>
						<CommonUpload
							multiple
							maxCount={10}
							extraData={{ LoaiTaiLieu: FileTypeConstant.DA_KeHoachNoiBo, ItemId: item.id }}
							onUpload={async (files, extraData) => {
								// Gọi API upload file kế hoạch triển khai nội bộ ở đây
								handleUpload(files, extraData ?? { LoaiTaiLieu: FileTypeConstant.DA_KeHoachNoiBo, ItemId: item.id });
							}}
							buttonText="Chọn file"
						/>
					</Modal>
				)}
				{isUploadedFilesModalOpen && (
					<UploadedFilesModal
						open={isUploadedFilesModalOpen}
						onClose={() => setIsUploadedFilesModalOpen(false)}
						files={uploadedFiles}
						onDelete={handleDeleteFile}
						title="Danh sách file kế hoạch triển khai nội bộ"
					/>
				)}

				{isPhanCongModalOpen && (
					<Modal
						title="Quản lý phân công thành viên"
						open={isPhanCongModalOpen}
						onCancel={handleClosePhanCongModal}
						onOk={handleSavePhanCong}
						okText="Lưu"
						cancelText="Đóng"
						width={800}
						destroyOnClose
					>
						<PhanCongTable
							phanCongList={phanCongList}
							userOptions={userOptions}
							roleOptions={roleOptions}
							onAdd={handleAddPhanCong}
							onChange={handlePhanCongChange}
							onDelete={handleDeletePhanCong}
						/>
					</Modal>
				)}

			</div>
		</div>
	);
};

export default DA_DuAnDetailView;
