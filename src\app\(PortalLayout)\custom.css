img {
    width: 100%;
}

.vertical-top {
    vertical-align: top;
}

.border-bottom {
    border-bottom: 1px solid #33333333;
}

.border-left {
    border-left: 1px solid #33333333;
}

.border-right {
    border-right: 1px solid #33333333;
}

.copyright-footer {
    display: flex;
    align-items: flex-start;
    flex-direction: row;
}

.swiper {
    width: 100%;
}

.swiper-slide img {
    width: 100%;
    height: auto;
}

.container-layout {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}

@media (width >=640px) {
    .container-layout {
        max-width: 640px;
    }
}

@media (width >=768px) {
    .container-layout {
        max-width: 768px;
    }
}

@media (width >=1024px) {
    .container-layout {
        max-width: 1024px;
    }
}

@media (width >=1280px) {
    .container-layout {
        max-width: 1280px;
    }
}

.pagination-btn {
    padding: 10px 15px;
    border: 1px solid #3592e3;
    border-radius: 2px;
    color: #000000;
    margin-top: 5px;
    margin-right: 5px;
    cursor: pointer;
}

.pagination-btn.active {
    background-color: #3592e3;
    color: #fff;
}

.pagination-btn:hover {
    background-color: #3592e3;
    color: #fff;
}


.custom-table {
    width: 100%;
    border-collapse: collapse;
    font-family: Arial, sans-serif;
    text-align: left;
    border: 1px solid #ddd;
}

/* Header (Tiêu đề cột) */
.custom-table thead {
    background-color: #f8f9fa;
    color: #333;
    font-weight: bold;
}

.custom-table thead th {
    padding: 12px;
    border-bottom: 2px solid #ddd;
}

/* Nội dung bảng */
.custom-table tbody tr {
    border-bottom: 1px solid #ddd;
}

.custom-table tbody tr:nth-child(even) {
    background-color: #f2f2f2; /* Màu xen kẽ */
}

.custom-table tbody td {
    padding: 10px;
}

/* Hover trên hàng */
.custom-table tbody tr:hover {
    background-color: #e6f7ff;
    transition: background-color 0.3s ease;
}

/* Nút Download */
.custom-table .download-icon {
    color: blue;
    cursor: pointer;
}

/* Style cho Pagination */
.pagination-container {
    text-align: center;
    margin-top: 10px;
}

.pagination-container a {
    margin: 0 5px;
    padding: 5px 10px;
    border: 1px solid #ccc;
    background-color: #f2f2f2;
    text-decoration: none;
    color: #333;
    border-radius: 4px;
}

.pagination-container a:hover {
    background-color: #007bff;
    color: white;
}

.pagination-container .active {
    font-weight: bold;
    background-color: blue;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
}

.print{
    margin-bottom: 10px;
}





.custom-table-modal {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ddd; /* Viền bảng */
    font-size: 16px;
}

.custom-table-modal tr > th,td {
    border: 1px solid #ddd;
}



.custom-table-modal td {
    border: 1px solid #ddd; /* Viền ô */
    padding: 10px; /* Khoảng cách nội dung */
}

.custom-table-modal tr:nth-child(even) {
    background-color: #f9f9f9; /* Nền hàng chẵn */
}

.custom-table-modal tr:nth-child(odd) {
    background-color: #fff; /* Nền hàng lẻ */
}

.custom-table-modal tr:hover {
    background-color: #f1f1f1; /* Hover hiệu ứng */
}

.custom-table-modal td:first-child {
    font-weight: bold;
    width: 30%;
    background-color: #f0f0f0; /* Màu nền cho cột label */
}

a:hover{
    color: #0073e6 !important;
}

.fieldsetWrapper {
    border: 1px solid #d9d9d9;
    padding: 16px;
    border-radius: 8px;
    position: relative;
    margin-bottom: 24px;
  }
  
  .legend {
    position: absolute;
    top: -12px;
    left: 12px;
    background: white;
    padding: 0 8px;
    font-weight: 600;
    font-size: 16px;
    color: #595959;
  }


  .search-container {
    margin-bottom: 15px;
}

.fieldset-search {
    border: 1px solid #ccc;
    border-radius: 6px;
    padding: 15px;
    background: #f9f9f9;
}

.legend-search {
    font-weight: bold;
    font-size: 16px;
    width: auto;
}

.search-fields {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.search-group {
    display: flex;
    flex-direction: column;
    width: calc(25% - 10px);
}

.search-group label {
    padding-bottom: 5px;
}

.search-group input {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    outline: none;
}

.search-btn {
    margin-top: 10px;
    padding: 8px 15px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.search-btn:hover {
    background: #0056b3;
}

.custom-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.custom-table th, .custom-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.custom-table thead {
    background-color: #007bff;
    color: white;
}

.custom-table tbody tr:nth-child(even) {
    background-color: #f2f2f2;
}

.view-detail {
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
}

.view-detail:hover {
    color: #0056b3;
}

@media (max-width: 768px) {
    .search-fields {
        flex-direction: column;
    }

    .search-group {
        width: 100%;
    }
}
