import React from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Form, Input, Select, DatePicker, Row } from "antd";
import { DownloadOutlined, SearchOutlined } from "@ant-design/icons";
import Flex from "@/components/shared-components/Flex";
import { downloadFileFromBase64 } from "@/utils/fileDownload";
import { useForm } from "antd/es/form/Form";
import { toast } from "react-toastify";
import * as extensions from "@/utils/extensions";

import { NS_NhanSuSearchType } from "@/types/nS_NhanSu/nS_NhanSu";
import nS_NhanSuService from "@/services/nS_NhanSu/nS_NhanSuService";

interface SearchProps {
  onFinish: ((values: NS_NhanSuSearchType) => void) | undefined;
  pageIndex: number;
  pageSize: number;
}
const Search: React.FC<SearchProps> = ({ onFinish, pageIndex, pageSize }) => {
  const [form] = useForm<NS_NhanSuSearchType>();

  const Export = async () => {
    const formValues = form.getFieldsValue();

    const exportData = {
      ...formValues,
      pageIndex,
      pageSize,
    };

    const response = await nS_NhanSuService.exportExcel(exportData);
    if (response.status) {
      downloadFileFromBase64(response.data, "Danh sách nhân Sự.xlsx");
    } else {
      toast.error(response.message);
    }
  };

  return (
    <>
      <Card className="customCardShadow mb-3">
        <Form
          form={form}
          layout="vertical"
          name="basic"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          initialValues={{ remember: true }}
          onFinish={onFinish}
          autoComplete="off"
        >
          <Row gutter={24}>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="chucVuId"
                label="Chức vụ"
                name="chucVuId"
              >
                <Input placeholder="Chức vụ" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="phongBanId"
                label="Phòng ban"
                name="phongBanId"
              >
                <Input placeholder="Phòng ban" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="ngaySinh"
                label="Ngày sinh"
                name="ngaySinh"
              >
                <Input placeholder="Ngày sinh" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="ngayVaoLam"
                label="Ngày vào làm"
                name="ngayVaoLam"
              >
                <Input placeholder="Ngày vào làm" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="gioiTinh"
                label="Giới tính"
                name="gioiTinh"
              >
                <Input placeholder="Giới tính" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="trangThai"
                label="Trạng thái"
                name="trangThai"
              >
                <Input placeholder="Trạng thái" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="maNV"
                label="Mã nhân viên"
                name="maNV"
              >
                <Input placeholder="Mã nhân viên" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="hoTen"
                label="Họ tên"
                name="hoTen"
              >
                <Input placeholder="Họ tên" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="cMND"
                label="CMND"
                name="cMND"
              >
                <Input placeholder="CMND" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="dienThoai"
                label="Điện thoại"
                name="dienThoai"
              >
                <Input placeholder="Điện thoại" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="email"
                label="Email"
                name="email"
              >
                <Input placeholder="Email" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="maSoThueCaNhan"
                label="Mã số thuế cá nhân"
                name="maSoThueCaNhan"
              >
                <Input placeholder="Mã số thuế cá nhân" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="soTaiKhoanNganHang"
                label="Số tài khoản ngân hàng"
                name="soTaiKhoanNganHang"
              >
                <Input placeholder="Số tài khoản ngân hàng" />
              </Form.Item>
            </Col>
            <Col xl={6} lg={8} md={12} xs={24}>
              <Form.Item<NS_NhanSuSearchType>
                key="nganHang"
                label="Ngân hàng"
                name="nganHang"
              >
                <Input placeholder="Ngân hàng" />
              </Form.Item>
            </Col>
          </Row>

          <Flex
            alignItems="center"
            justifyContent="center"
            className="btn-group"
          >
            <Button
              type="primary"
              htmlType="submit"
              icon={<SearchOutlined />}
              size="small"
            >
              Tìm kiếm
            </Button>
            <Button
              onClick={Export}
              type="primary"
              icon={<DownloadOutlined />}
              className="colorKetXuat"
              size="small"
            >
              Kết xuất
            </Button>
          </Flex>
        </Form>
      </Card>
    </>
  );
};

export default Search;
