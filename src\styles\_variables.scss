// App Colors
$border-color: #cfcfcf;
$item-hover-color: rgb(0, 0, 0, 0.05);
//

// Resuable Styles
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin overflow-y-scroll {
  overflow-y: auto !important;

  &::-webkit-scrollbar {
    width: 5px;
    height: 8px;
    padding-top: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--file-manager-primary-color) !important;
    border-radius: 8px;
  }
}

//