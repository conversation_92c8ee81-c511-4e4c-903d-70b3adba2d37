{"name": "ems", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3005", "lint": "next lint"}, "dependencies": {"@amcharts/amcharts5": "^5.11.0", "@ant-design/icons": "^5.5.2", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@editorjs/editorjs": "^2.30.8", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.2", "@editorjs/list": "^2.0.4", "@editorjs/paragraph": "^2.11.7", "@editorjs/table": "^2.4.3", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@nosferatu500/react-sortable-tree": "^4.4.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@reduxjs/toolkit": "^2.5.0", "@types/react-beautiful-dnd": "^13.1.8", "@uiw/react-codemirror": "^4.23.9", "antd": "^5.23.0", "axios": "^1.7.9", "canvas": "^3.1.0", "ckeditor4-react": "^4.3.0", "cropperjs": "^1.5.13", "dayjs": "^1.11.13", "diff": "^7.0.0", "dompurify": "^3.2.4", "exceljs": "^4.4.0", "framer-motion": "^11.16.0", "grapesjs": "^0.22.5", "grapesjs-blocks-basic": "^1.0.2", "grapesjs-component-countdown": "^1.0.2", "grapesjs-custom-code": "^1.0.2", "grapesjs-plugin-export": "^1.0.12", "grapesjs-plugin-forms": "^2.0.6", "grapesjs-preset-webpage": "^1.0.3", "html-react-parser": "^5.2.3", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "next": "14.2.22", "nprogress": "^0.2.0", "pdfjs-dist": "^3.11.174", "react": "^18.2.0", "react-collapsed": "^4.2.0", "react-custom-scrollbars-2": "^4.5.0", "react-dnd": "^16.0.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-easy-crop": "^5.2.0", "react-grid-layout": "^1.5.0", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-resizable": "^3.0.5", "react-sortablejs": "^6.1.4", "react-toastify": "^11.0.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sass": "^1.86.3", "sortablejs": "^1.15.6", "string-similarity": "^4.0.4", "swiper": "^11.2.4", "uuid": "^11.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@types/lodash": "^4.17.16", "@types/node": "20.17.14", "@types/nprogress": "^0.2.3", "@types/react": "18.3.18", "@types/react-dom": "^18", "@types/react-grid-layout": "^1.3.5", "eslint": "^8", "eslint-config-next": "14.2.22", "typescript": "5.7.3"}}