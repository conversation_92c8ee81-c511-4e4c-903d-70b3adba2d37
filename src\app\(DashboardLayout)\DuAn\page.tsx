"use client";
import { useCallback, useEffect, useState } from "react";
import Flex from "@/components/shared-components/Flex";
import { ResponsePageList } from "@/types/general";
import withAuthorization from "@/libs/authentication";
import { setIsLoading } from "@/store/general/GeneralSlice";
import { useSelector } from "@/store/hooks";
import { AppDispatch } from "@/store/store";
import {
  CloseOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  EyeOutlined,
  PlusCircleOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Dropdown,
  FormProps,
  MenuProps,
  Modal,
  Pagination,
  Space,
  Table,
  TableProps,
  Tabs,
} from "antd";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import Search from "./search";
import AutoBreadcrumb from "@/components/util-compenents/Breadcrumb";
import {
  DA_DuAnCreateOrUpdateType,
  DA_DuAnSearchType,
  DA_DuAnType,
} from "@/types/dA_DuAn/dA_DuAn";
import dA_DuAnService from "@/services/dA_DuAn/dA_DuAnService";
import DA_DuAnCreateOrUpdate from "./createOrUpdate";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";

const DA_DuAnPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const [data, setData] = useState<ResponsePageList<DA_DuAnType[]>>();
  const [pageSize, setPageSize] = useState<number>(20);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [isPanelVisible, setIsPanelVisible] = useState<boolean>(false);
  const [searchValues, setSearchValues] = useState<DA_DuAnSearchType | null>(
    null
  );
  const loading = useSelector((state) => state.general.isLoading);
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [currentItem, setCurentItem] =
    useState<DA_DuAnCreateOrUpdateType | null>(null);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>("1");

  const tableColumns: TableProps<DA_DuAnType>["columns"] = [
    {
      title: "STT",
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: "Ngày bắt đầu",
      dataIndex: "ngayBatDau",
      render: (_: any, record: DA_DuAnType) => (
        <span>
          {record.ngayBatDau
            ? dayjs(record.ngayBatDau).format("DD/MM/YYYY")
            : ""}
        </span>
      ),
    },
    {
      title: "Ngày kết thúc",
      dataIndex: "ngayKetThuc",
      render: (_: any, record: DA_DuAnType) => (
        <span>
          {record.ngayKetThuc
            ? dayjs(record.ngayKetThuc).format("DD/MM/YYYY")
            : ""}
        </span>
      ),
    },
    {
      title: "Tên dự án",
      dataIndex: "tenDuAn",
      render: (_: any, record: DA_DuAnType) => <span>{record.tenDuAn}</span>,
    },
    {
      title: "Ngày tiếp nhận",
      dataIndex: "ngayTiepNhan",
      render: (_: any, record: DA_DuAnType) => (
        <span>
          {record.ngayTiepNhan
            ? dayjs(record.ngayTiepNhan).format("DD/MM/YYYY")
            : ""}
        </span>
      ),
    },
    {
      title: "Trạng thái thực hiện",
      dataIndex: "trangThaiThucHien",
      render: (_: any, record: DA_DuAnType) => (
        <span>{record.trangThaiThucHien}</span>
      ),
    },
    {
      title: "Thời gian cài đặt máy chủ",
      dataIndex: "timeCaiDatMayChu",
      render: (_: any, record: DA_DuAnType) => (
        <span>
          {record.timeCaiDatMayChu
            ? dayjs(record.timeCaiDatMayChu).format("DD/MM/YYYY HH:mm")
            : ""}
        </span>
      ),
    },
    {
      title: "Backup dữ liệu?",
      dataIndex: "isBackupMayChu",
      render: (_: any, record: DA_DuAnType) => (
        <span>{record.isBackupMayChu ? "Có" : "Không"}</span>
      ),
    },
    {
      title: "Link demo",
      dataIndex: "linkDemo",
      render: (_: any, record: DA_DuAnType) => <span>{record.linkDemo}</span>,
    },
    {
      title: "Link thực tế",
      dataIndex: "linkThucTe",
      render: (_: any, record: DA_DuAnType) => <span>{record.linkThucTe}</span>,
    },
    {
      title: "Mô tả dự án",
      dataIndex: "moTaDuAn",
      render: (_: any, record: DA_DuAnType) => (
        <span
          dangerouslySetInnerHTML={{
            __html: record.moTaDuAn || "",
          }}
        />
      ),
    },
    {
      title: "Yêu cầu dự án",
      dataIndex: "yeuCauDuAn",
      render: (_: any, record: DA_DuAnType) => (
        <span
          dangerouslySetInnerHTML={{
            __html: record.yeuCauDuAn || "",
          }}
        />
      ),
    },
    {
      title: "Thao tác",
      dataIndex: "actions",
      fixed: "right",
      align: "center",
      render: (_: any, record: DA_DuAnType) => {
        const items: MenuProps["items"] = [
          {
            label: "Chi tiết",
            key: "2",
            icon: <EyeOutlined />,
            onClick: () => {
              handleShowDetailPage(record.id);
            },
          },
          {
            label: "Chỉnh sửa",
            key: "3",
            icon: <EditOutlined />,
            onClick: () => {
              handleShowModal(true, record);
            },
          },
          {
            type: "divider",
          },
          {
            label: "Xóa",
            key: "4",
            danger: true,
            icon: <DeleteOutlined />,
            onClick: () => setConfirmDeleteId(record.id ?? ""),
          },
        ];
        return (
          <>
            <Dropdown menu={{ items }} trigger={["click"]}>
              <Button
                onClick={(e) => e.preventDefault()}
                color="primary"
                size="small"
              >
                <Space>
                  Thao tác
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
          </>
        );
      },
    },
  ];

  const hanleCreateEditSuccess = () => {
    handleLoadData();
    setCurentItem(null);
  };

  const handleDelete = async () => {
    const response = await dA_DuAnService.delete(confirmDeleteId ?? "");
    if (response.status) {
      toast.success("Xóa thành công");
      handleLoadData();
    }
    setConfirmDeleteId(null);
  };

  const toggleSearch = () => {
    setIsPanelVisible(!isPanelVisible);
  };

  const onFinishSearch: FormProps<DA_DuAnSearchType>["onFinish"] = async (
    values
  ) => {
    try {
      setSearchValues(values);
      await handleLoadData(values);
    } catch (error) {
      console.error("Lỗi khi lưu dữ liệu:", error);
    }
  };

  const handleLoadData = useCallback(
    async (searchDataOverride?: DA_DuAnSearchType) => {
      dispatch(setIsLoading(true));

      const searchData = searchDataOverride || {
        pageIndex,
        pageSize,
        ...(searchValues || {}),
      };
      const response = await dA_DuAnService.getData(searchData);
      if (response != null && response.data != null) {
        const data = response.data;
        setData(data);
      }
      dispatch(setIsLoading(false));
    },
    [dispatch, pageIndex, pageSize, searchValues]
  );

  const handleShowDetailPage = (id?: string) => {
    if (id) {
      router.push(`/DuAn/detail/${id}`);
    }
  };

  const handleShowModal = async (isEdit?: boolean, item?: DA_DuAnType) => {
    setIsOpenModal(true);
    if (isEdit && item?.id) {
      // Gọi API lấy chi tiết dự án
      const res = await dA_DuAnService.getFormById(item.id);
      console.log("res.data", res.data);
      setCurentItem(res.data); // res.data phải có phanCongList
    } else {
      setCurentItem(null);
    }
  };

  const handleClose = () => {
    setIsOpenModal(false);
    setCurentItem(null);
  };

  useEffect(() => {
    handleLoadData();
  }, [handleLoadData]);

  
  return (
    <>
      <Flex
        alignItems="center"
        justifyContent="space-between"
        className="mb-2 flex-wrap justify-content-end"
      >
        <AutoBreadcrumb />
        <div className="btn-group">
          <Button
            onClick={() => toggleSearch()}
            type="primary"
            size="small"
            icon={isPanelVisible ? <CloseOutlined /> : <SearchOutlined />}
          >
            {isPanelVisible ? "Ẩn tìm kiếm" : "Tìm kiếm"}
          </Button>
          <Button
            onClick={() => {
              handleShowModal();
            }}
            type="primary"
            icon={<PlusCircleOutlined />}
            size="small"
          >
            Thêm mới
          </Button>
          {isOpenModal && (
            <DA_DuAnCreateOrUpdate
              onSuccess={hanleCreateEditSuccess}
              onClose={handleClose}
              itemId={currentItem?.id ? currentItem.id : null}
              item={currentItem}
            />
          )}
        </div>
      </Flex>
      {isPanelVisible && (
        <Search
          onFinish={onFinishSearch}
          pageIndex={pageIndex}
          pageSize={pageSize}
        />
      )}

      {confirmDeleteId && (
        <Modal
          title="Xác nhận xóa"
          open={true}
          onOk={handleDelete}
          onCancel={() => setConfirmDeleteId(null)}
          okText="Xóa"
          cancelText="Hủy"
        >
          <p>Bạn có chắc chắn muốn xóa mục này?</p>
        </Modal>
      )}
      <Card className={"customCardShadow"}>
        <div className="table-responsive">
          <Table
            columns={tableColumns}
            bordered
            dataSource={data?.items}
            rowKey="id"
            scroll={{ x: "max-content" }}
            pagination={false}
            loading={loading}
          />
        </div>
        <Pagination
          className="mt-2"
          total={data?.totalCount}
          showTotal={(total, range) =>
            `${range[0]}-${range[1]} trong ${total} dữ liệu`
          }
          pageSize={pageSize}
          defaultCurrent={1}
          onChange={(e) => {
            setPageIndex(e);
          }}
          onShowSizeChange={(current, pageSize) => {
            setPageIndex(current);
            setPageSize(pageSize);
          }}
          size="small"
          align="end"
        />
      </Card>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        tabPosition="top"
        destroyInactiveTabPane
      />
    </>
  );
};

export default withAuthorization(DA_DuAnPage, "");
