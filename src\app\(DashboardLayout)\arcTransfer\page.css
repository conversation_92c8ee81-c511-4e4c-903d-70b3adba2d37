.page-header {
    background: linear-gradient(to right, #f8f9fa, #ffffff);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    padding: 12px 16px;
    margin-bottom: 16px !important;
    transition: all 0.3s ease;
}

.page-header:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.ant-table-thead > tr > th {
    background-color: #f0f5ff !important;
    font-weight: 600 !important;
    color: #1a3353 !important;
    border-bottom: 2px solid #d9e6ff !important;
}

.ant-table-tbody > tr:hover > td {
    background-color: #f0f7ff !important;
}

.ant-table-tbody > tr > td {
    padding: 12px 16px !important;
    transition: all 0.2s;
}

.ant-pagination-item-active {
    border-color: #1890ff !important;
    font-weight: 600 !important;
}

.ant-card {
    border-radius: 8px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08) !important;
    transition: all 0.3s ease !important;
}

.ant-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.card-title {
    font-size: 1.25rem !important;
    color: #2e3b52 !important;
    font-weight: 600 !important;
    padding-bottom: 8px !important;
    border-bottom: 2px solid #f0f0f0 !important;
    margin-bottom: 16px !important;
}

.search-container .ant-input-search {
    border-radius: 6px !important;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05) !important;
}

.search-container .ant-input,
.search-container .ant-btn {
    border-radius: 6px !important;
}

.action-button {
    background: #f9fafc !important;
    border-color: #d9e1f2 !important;
    color: #2e3b52 !important;
    transition: all 0.2s !important;
}

.action-button:hover {
    background: #f0f5ff !important;
    border-color: #b7ceff !important;
}

/* Fancy button styles */
.btn-primary {
    background: linear-gradient(45deg, #1890ff, #69c0ff) !important;
    border: none !important;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.4) !important;
    transition: all 0.3s !important;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #096dd9, #40a9ff) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.5) !important;
}

.btn-refresh {
    background: white !important;
    border-color: #d9e1f2 !important;
    color: #2e3b52 !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    transition: all 0.2s !important;
}

.btn-refresh:hover {
    background: #f9fafc !important;
    transform: rotate(45deg) !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

.arc-transfer-modal .ant-modal-content {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-container {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 8px;
}

.form-container::-webkit-scrollbar {
    width: 5px;
}

.form-container::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 8px;
}

.form-section {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: #f5222d;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px dashed #ffccc7;
}

.text-primary {
    color: #1890ff;
}

.pdf-preview-modal .ant-modal-content {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#pdf-content {
    background-color: white;
    padding: 20px;
}

#pdf-content::-webkit-scrollbar {
    width: 5px;
}

#pdf-content::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 8px;
}

.arc-transfer-modal .ant-upload-list-item:not(.ant-upload-list-item-error) .ant-upload-list-item-name,
.arc-transfer-modal .ant-upload-list-item:not(.ant-upload-list-item-error) .ant-upload-list-item-name a {
    color: #1890ff !important; /* Changed to blue */
}

.ant-modal .ant-modal-footer {
    /* background: #f0f2f5; */
    border-radius: 0 0 8px 8px;
    padding: 8px;
    display: flex
;
    justify-content: center !important;
    align-items: center;
    border-radius: 0 0 8px 8px !important;
}

.custom-table .even-row td {
    background-color: #f9f9f9; /* Light gray for even rows */
}

.custom-table .odd-row td {
    background-color: #ffffff; /* White for odd rows */
}

/* Optional: Style for row hover to maintain consistency */
.custom-table .ant-table-tbody > tr.ant-table-row:hover > td {
    background: #e6f7ff; /* Ant Design's default hover color or your preferred one */
}