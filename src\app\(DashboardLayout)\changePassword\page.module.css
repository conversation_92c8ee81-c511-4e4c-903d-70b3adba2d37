.mgbottom10 {
  margin-bottom: 16px;
}

.passwordForm {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.passwordForm :global(.ant-form-item) {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.passwordForm :global(.ant-form-item-label) {
  font-weight: 500;
}

.passwordForm :global(.ant-input-affix-wrapper) {
  border-radius: 6px;
  padding: 10px 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.passwordForm :global(.ant-input-affix-wrapper:hover) {
  border-color: #40a9ff;
}

.passwordForm :global(.ant-input-affix-wrapper:focus),
.passwordForm :global(.ant-input-affix-wrapper-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.submitButton {
  min-width: 120px;
  height: 40px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submitButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cardWrapper {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
