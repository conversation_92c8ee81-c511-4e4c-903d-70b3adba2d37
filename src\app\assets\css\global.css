/* stylelint-disable */
/* @import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap'); */
@import "tailwindcss";

@theme {
  --color-primary: #2a5da3;
  --color-primary-hover: #4a8fd6;
  --color-primary-100: rgba(206, 17, 39, 0.1);
}

html {
  scroll-behavior: smooth;
}

html,
body {
  width: 100%;
  height: 100%;
}
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  /* font-family: sans-serif; */

  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
@-ms-viewport {
  width: device-width;
}
body {
  margin: 0;
}
[tabindex="-1"]:focus {
  outline: none;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 500;
}
p {
  margin-top: 0;
  margin-bottom: 1em;
}
abbr[title],
abbr[data-original-title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline;
  text-decoration: underline dotted;
  border-bottom: 0;
  cursor: help;
}
address {
  margin-bottom: 1em;
  font-style: normal;
  line-height: inherit;
}
input[type="text"],
input[type="password"],
input[type="number"],
textarea {
  -webkit-appearance: none;
}
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1em;
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 500;
}
dd {
  margin-bottom: 0.5em;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1em;
}
dfn {
  font-style: italic;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
pre,
code,
kbd,
samp {
  font-size: 1em;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier,
    monospace;
}
pre {
  margin-top: 0;
  margin-bottom: 1em;
  overflow: auto;
}
figure {
  margin: 0 0 1em;
}
img {
  vertical-align: middle;
  border-style: none;
}
a,
area,
button,
[role="button"],
input:not([type="range"]),
label,
select,
summary,
textarea {
  touch-action: manipulation;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75em;
  padding-bottom: 0.3em;
  text-align: left;
  caption-side: bottom;
}
input,
button,
select,
optgroup,
textarea {
  margin: 0;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html [type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  margin: 0;
  padding: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  margin-bottom: 0.5em;
  padding: 0;
  color: inherit;
  font-size: 1.5em;
  line-height: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
mark {
  padding: 0.2em;
  background-color: #feffe6;
}

/* CSS cho banner swiper */
.swiper .swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* .ant-menu-submenu-title {
  padding: 0 !important;
} */

.ant-menu-item {
  padding: 10px !important;
  display: flex !important;
  align-items: center;
  justify-content: start;
}

.ant-menu.ant-menu-root {
  border-right: none !important;
}

.menu-min .toggle-button {
  justify-content: center;
}

.menu-min .ant-menu-submenu-title {
  /* display: flex; */
  justify-content: center;
  align-items: center;
}

.menu-min .ant-menu-title-content {
  display: none;
}

.btn-group .btn:last-child {
  margin-right: 0 !important;
}

.ant-table-wrapper
  .ant-table.ant-table-bordered
  > .ant-table-container
  table
  thead
  tr
  th,
.ant-table-wrapper
  .ant-table.ant-table-bordered
  > .ant-table-container
  table
  thead
  tr
  td,
.ant-table-wrapper
  .ant-table.ant-table-bordered
  > .ant-table-container
  table
  tbody
  tr
  th,
.ant-table-wrapper
  .ant-table.ant-table-bordered
  > .ant-table-container
  table
  tbody
  tr
  td,
.ant-table-wrapper
  .ant-table.ant-table-bordered
  > .ant-table-container
  table
  tfoot
  tr
  th,
.ant-table-wrapper
  .ant-table.ant-table-bordered
  > .ant-table-container
  table
  tfoot
  tr
  td {
  border-inline-end: 1px solid #d1d5dc;
}

.ant-card {
  border-radius: 0px !important;
}

.ant-btn,
.ant-btn-sm {
  border-radius: 4px !important;
}

.ant-pagination .ant-pagination-item-active {
  background-color: transparent !important;
  border-radius: 4px !important;
  color: var(--color-primary) !important;
  border: 1px solid var(--color-primary) !important;
}

.ant-pagination .ant-pagination-item-active a {
  color: var(--color-primary) !important;
  font-weight: 500 !important;
}

.ant-spin .ant-spin-dot-item {
  background: var(--color-primary) !important;
}

.ant-btn-variant-solid {
  background: var(--color-primary) !important;
}

.ant-btn-variant-solid:hover,
.ant-btn-primary:hover {
  background-color: var(--color-primary-hover) !important;
}

.ant-card .ant-card-body {
  border-radius: 0 !important;
}

.ant-table-wrapper .ant-table {
  border-radius: 0 !important;
}

.ant-table-wrapper .ant-table-container {
  border-start-start-radius: 0 !important;
  border-start-end-radius: 0 !important;
}

.ant-pagination .ant-pagination-item {
  border-radius: 4px !important;
}

.toggle-button:hover {
  background-color: var(--color-primary-hover);
  transition: background-color 0.3s ease-in-out;
}

.ant-modal .ant-modal-close {
  top: unset !important;
}
.ant-menu-light .ant-menu-item-selected,
.ant-menu-light > .ant-menu .ant-menu-item-selected {
  color: var(--color-primary) !important;
}

.ant-menu-light.ant-menu-inline .ant-menu-item::after,
.ant-menu-light > .ant-menu.ant-menu-inline .ant-menu-item::after {
  border-inline-end: 2px solid var(--color-primary) !important;
}

.ant-menu-light .ant-menu-item-selected,
.ant-menu-light > .ant-menu .ant-menu-item-selected {
  background-color: var(--color-primary-100);
}

.ant-menu-light .ant-menu-item a,
.ant-menu-light > .ant-menu .ant-menu-item a,
.ant-menu-light .ant-menu-item a:hover,
.ant-menu-light > .ant-menu .ant-menu-item a:hover {
  color: var(--color-primary) !important;
}

.ant-menu-light
  .ant-menu-item:not(.ant-menu-item-selected):not(
    .ant-menu-submenu-selected
  ):hover,
.ant-menu-light
  > .ant-menu
  .ant-menu-item:not(.ant-menu-item-selected):not(
    .ant-menu-submenu-selected
  ):hover,
.ant-menu-light
  .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected)
  > .ant-menu-submenu-title:hover,
.ant-menu-light
  > .ant-menu
  .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected)
  > .ant-menu-submenu-title:hover {
  color: var(--color-primary) !important;
}

.ant-menu .ant-menu-item,
.ant-menu .ant-menu-submenu,
.ant-menu .ant-menu-submenu-title {
  border-radius: 4px !important;
  /* padding-left: 8px; */
}

.menu-max .ant-menu .ant-menu-item,
.menu-max .ant-menu .ant-menu-submenu,
.menu-max .ant-menu .ant-menu-submenu-title {
  padding-left: 8px !important;
}

.menu-max .ant-menu .ant-menu-submenu {
  padding-left: 0px !important;
}

.menu-max .ant-menu .ant-menu-item {
  padding-left: 24px !important;
}

.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-submenu-title:active,
.ant-menu-light
  > .ant-menu:not(.ant-menu-horizontal)
  .ant-menu-submenu-title:active {
  background-color: var(--color-primary-100);
}

.ant-modal .ant-modal-content {
  border-radius: 8px !important;
}

.ant-menu .ant-menu-submenu-arrow::before,
.ant-menu .ant-menu-submenu-arrow::after {
  height: 1.2px;
}

.ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover {
  background: transparent;
}

.ant-menu-submenu-popup > .ant-menu {
  border-radius: 8px !important;
}

.custom-progress .ant-progress-bg,
.custom-progress .ant-progress-inner {
  border-radius: 0 !important;
}

#nprogress {
  pointer-events: none;
}

/* Ẩn spinner */
#nprogress .spinner {
  display: none !important;
}

#nprogress .peg {
  display: none !important;
  /* background-color: red !important; */
}

#nprogress .bar {
  background: var(--color-amber-400) !important;
  position: fixed !important;
  z-index: 999 !important;
  top: 70px !important;
  left: 0 !important;
  width: 101vw !important;
  height: 1px !important;
  /* transform: none !important; */
}

.table_component {
  overflow: auto;
  width: 100%;
}

.table_component table {
  border: 1px solid #dededf;
  height: 100%;
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border-spacing: 1px;
  text-align: left;
}

.table_component caption {
  caption-side: top;
  text-align: left;
}

.table_component th {
  border: 1px solid #dededf;
  background-color: #eceff1;
  color: #000000;
  padding: 5px;
}

.table_component td {
  border: 1px solid #dededf;
  background-color: #ffffff;
  color: #000000;
  padding: 5px;
}

.form-suffix .ant-input-suffix {
  position: absolute;
  top: 0;
  right: 0;
}

.ant-drawer .ant-drawer-header {
  padding: 5px;
}

.modal-full .ant-modal-body {
  height: calc(100vh - 220px);
}

.ant-table-cell mark {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.tabs-custom .ant-tabs > .ant-tabs-nav {
  margin-bottom: 0;
}

.ant-modal .ant-modal-close {
  border-radius: 0 6px 0 0;
}

.ant-modal .ant-modal-close:hover {
  background-color: var(--color-primary);
  color: white !important;
}

.ant-modal .ant-modal-close:hover .ant-modal-close-x {
  color: white;
}

.ant-table-cell .ant-ribbon {
  top: -8px;
  /*right: -16px !important; */
}

.ant-pagination .ant-pagination-item {
  background-color: unset !important;
}

.ant-pagination .ant-pagination-item:hover {
  background-color: rgba(69, 85, 96, 0.06) !important;
}

/* .ant-modal-body {
  max-height: calc(100vh - 150px);
  overflow-y: auto;
} */

.custom-modal .ant-tabs-content-holder {
  width: 100%;
  overflow: hidden;
  max-height: calc(100vh - 250px);
  overflow-y: auto;
}

.ant-table-cell.ant-table-row-expand-icon-cell {
  border-inline-end: 1px solid transparent !important;
}

.ant-table-wrapper
  .ant-table-tbody
  > tr
  > td
  > .ant-table-expanded-row-fixed
  > .ant-table-wrapper:only-child
  .ant-table {
  margin-block: 0;
  margin-inline: 0;
}

.custom-card tr > th,
.custom-card .ant-table-thead tr th {
  background-color: #fff2cc !important;
}

.custom-card table {
  border: 1px solid var(--color-gray-100);
  border-radius: 0;
}

.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
  padding: 8px;
}

.ant-tabs-left
  > .ant-tabs-content-holder
  > .ant-tabs-content
  > .ant-tabs-tabpane {
  padding-left: 8px;
}

.ant-tabs-tab-btn .ant-badge {
  color: inherit !important;
}

.ant-table-expanded-row.ant-table-expanded-row-level-1 {
  background-color: #fff2cc !important;
  position: relative;
}

.ant-table-expanded-row.ant-table-expanded-row-level-1::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 3px;
  background-color: var(--color-primary);
}

.danh-sach-don-thu .ant-table-expanded-row.ant-table-expanded-row-level-1 {
  background-color: transparent !important;
  position: relative;
}

.danh-sach-don-thu .ant-table-wrapper .ant-table-thead > tr > th {
  background-color: #fff2cc !important;
}

.danh-sach-don-thu .ant-table-wrapper {
  border: 1px solid var(--color-gray-200);
}

.truncate-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.truncate-3-lines {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tabs-left-custom .ant-tabs-tab-btn,
.tabs-left-custom .ant-tabs-tab-btn .ant-badge {
  width: 100%;
  text-align: left;
}
.ant-form-item .ant-form-item-label > label {
  color: #222;
}
.ant-drawer .ant-drawer-close {
  color: rgba(69, 85, 96, 0.45) !important;
}

.ant-drawer .ant-drawer-close:hover {
  background-color: var(--color-primary);
  color: white !important;
}
