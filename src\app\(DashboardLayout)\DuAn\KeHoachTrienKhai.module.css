.btn-them {
  background: #1677ff;
  color: #fff;
  border-radius: 6px;
  font-weight: 500;
  padding: 0 14px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 1px 2px rgba(22,119,255,0.06);
  border: none;
  transition: background 0.2s;
}
.btn-them:hover, .btn-them:focus {
  background: #0958d9;
  color: #fff;
}

.customTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  font-size: 14px;
  border-left: none !important;
  border-right: none !important;
}
.customTable th,
.customTable td {
  border-bottom: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  padding: 8px 16px;
  font-size: 14px;
  color: #262626;
  background: #fff;
  transition: background 0.2s;
  border-left: none !important;
  border-right: none !important;
  border-radius: 0 !important;
}
.customTable th {
  background: rgb(240, 240, 240);
  font-weight: bold;
  color: #000;
  border-bottom: 2px solid #f0f0f0;
  text-align: left;
  font-size: 14px;
  letter-spacing: 0;
  text-transform: none;
  padding: 8px 16px;
  border-radius: 0 !important;
}
.customTable tr:last-child td {
  border-bottom: none;
}
.customTable tr td:last-child,
.customTable tr th:last-child {
  border-right: none;
}
.customTable tbody tr:nth-child(odd) td {
  background: #fff;
}
.customTable tbody tr:nth-child(even) td {
  background: #fafbfc;
}
.customTable tr:hover td {
  background: #e6f4ff !important;
}
.customTable .ant-picker,
.customTable .ant-picker-input > input {
  height: 32px !important;
  min-height: 32px !important;
  line-height: 32px !important;
  font-size: 14px;
  border-radius: 6px;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  display: flex;
  align-items: center;
}

.customTable .ant-picker-input {
  height: 32px !important;
  min-height: 32px !important;
  display: flex;
  align-items: center;
}

.customTable .ant-btn-link {
  padding: 0 4px;
}
.customTable tr:hover {
  background: #f5f7fa;
}
.btnThem {
  background: #f4f6fb;
  color: #1677ff;
  border-radius: 16px;
  font-weight: 500;
  padding: 0 10px;
  height: 28px;
  min-width: 60px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  box-shadow: none;
  border: 1px solid #e0e3ea;
  transition: background 0.2s, color 0.2s, border 0.2s;
}
.btnThem:hover, .btnThem:focus {
  background: #e6f4ff;
  color: #0958d9;
  border: 1px solid #b6d6f6;
}

.iconBtn {
  padding: 0 !important;
  border: none;
  background: #f4f6fb;
  color: #1677ff;
  font-size: 18px;
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s, color 0.2s;
  box-shadow: none;
}
.iconBtn:hover,
.iconBtn:focus {
  background: #e6f4ff;
  color: #0958d9;
}

.inputTime {
  height: 38px !important;
  min-height: 38px !important;
  line-height: 38px !important;
  font-size: 14px;
  border-radius: 6px;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  display: flex;
  align-items: center;
}
.inputTime .ant-picker-input > input {
  height: 38px !important;
  min-height: 38px !important;
  line-height: 38px !important;
  font-size: 14px;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.inputTime :global(.ant-picker),
.inputTime :global(.ant-picker-range) {
  height: 38px !important;
  min-height: 38px !important;
  box-sizing: border-box;
}

.inputAssign :global(.ant-select-selector) {
  height: 32px !important;
  min-height: 32px !important;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.inputAssign :global(.ant-select-selection-search-input) {
  height: 38px !important;
  min-height: 38px !important;
}

.moTaDuAnHtml {
  white-space: pre-line;
  word-break: break-word;
}

.customModal :global(.ant-modal-wrap) {
  margin-top: 24px !important;
}

.input32 {
  height: 32px !important;
  min-height: 32px !important;
  line-height: 32px !important;
  box-sizing: border-box;
}
.input32 :global(.ant-picker),
.input32 :global(.ant-picker-input > input),
.input32 :global(.ant-select-selector),
.input32 :global(.ant-select-single .ant-select-selector),
.input32 :global(.ant-select-single .ant-select-selector input) {
  height: 38px !important;
  min-height: 38px !important;
  line-height: 38px !important;
  box-sizing: border-box;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.input32 :global(.ant-picker-input > input) {
  height: 38px !important;
  min-height: 38px !important;
  line-height: 38px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}