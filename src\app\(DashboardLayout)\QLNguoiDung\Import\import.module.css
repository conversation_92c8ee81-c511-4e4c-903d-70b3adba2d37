.customCardShadow {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    /* <PERSON><PERSON>u và độ mờ */
    border-radius: 8px;
    /* Tùy chỉnh bo góc */
}

.th {
    text-align: left;
    width: 200px;
}

.tableCauHinh {
    border-collapse: collapse; /* Gộp viền */
    width: 100%; /* Đ<PERSON> bảng rộng toàn chiều ngang */
}

.tableCauHinh, .thCss, .tdCss {
    border: 1px solid #ccc; /* Thêm viền cho bảng, ô tiêu đề và ô dữ liệu */
}

.thCss, .tdCss {
    padding: 8px; /* Thêm khoảng cách trong ô */
    text-align: left; /* Căn trái nội dung */
}

.thCss {
    background-color: #f2f2f2; /* <PERSON><PERSON>u nền cho tiêu đề */
}

.width100Per {
    width: 100%;
}

.center {
    text-align: center;
}