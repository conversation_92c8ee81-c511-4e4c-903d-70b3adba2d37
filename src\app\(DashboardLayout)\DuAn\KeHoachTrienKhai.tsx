import React, { useState, useEffect, useCallback } from "react";
import { Input, But<PERSON>, DatePicker, Modal, Select, Checkbox, Form, Drawer } from "antd";
import { EditOutlined, DeleteOutlined, SaveOutlined, CloseOutlined, PlusOutlined, MenuOutlined } from "@ant-design/icons";
import styles from './KeHoachTrienKhai.module.css';
import { DropdownOption } from "@/types/general";
import dayjs from "dayjs";
import { userService } from "@/services/user/user.service";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { DA_KeHoachThucHienCreateOrUpdateType } from "@/types/dA_DuAn/dA_KeHoachThuc<PERSON>ien";
import dA_KeHoachThucHienService from "@/services/dA_KeHoachThucHien/dA_KeHoachThucHienService";
import { toast } from "react-toastify";

interface RowType {
  key: string;
  stt: number | string;
  group?: string;
  isGroup?: boolean;
  ngayBatDau?: string | null;
  ngayKetThuc?: string | null;
  canhBaoTruocNgay?: number | null;
  isKeHoachNoiBo?: boolean | null;
  isCanhBao?: boolean | null;
  noiDungCongViec?: string | null;
  phanCong?: string | null;
}

interface Props {
  item: DA_KeHoachThucHienCreateOrUpdateType[] | null;
  idDuAn: string | null;
  iskeHoachNoiBo?: boolean | null;
  onClose?: () => void;
}

const DEFAULT_ROW: Omit<RowType, 'key' | 'stt' | 'group'> = {
  ngayBatDau: null,
  ngayKetThuc: null,
  canhBaoTruocNgay: 0,
  isKeHoachNoiBo: false,
  isCanhBao: false,
  noiDungCongViec: "",
  phanCong: "",
};

// DraggableRow phải là component độc lập ngoài component cha để tránh lỗi hook
const DraggableRow = ({ row, children, ...props }: any) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: row.key,
  });
  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    background: isDragging ? "#fafafa" : undefined,
    cursor: "move",
  };
  return (
    <tr ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </tr>
  );
};

const KeHoachTrienKhaiCustomTable: React.FC<Props> = ({ item, idDuAn, iskeHoachNoiBo, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [editingRow, setEditingRow] = useState<RowType>({} as RowType);
  const [data, setData] = useState<RowType[]>([]);
  const [userOptions, setUserOptions] = useState<any[]>([]); // Thêm khai báo userOptions

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 5 } })
  );

  useEffect(() => {
    userService.getDropdown().then(userRes => {
      setUserOptions(Array.isArray(userRes.data) ? userRes.data : []);
    });
  }, []);

  // Map dữ liệu API trả về sang state data khi item thay đổi
  useEffect(() => {
    let list: any[] = [];
    if (Array.isArray(item)) {
      list = item;
    } else if (item && Array.isArray((item as any).keHoachThucHienList)) {
      list = (item as any).keHoachThucHienList;
    }
    // Tạo map groupId -> key cho group
    const groupMap: Record<string, string> = {};
    let groupIndex = 0;
    // Đầu tiên tạo các group, gán key và stt
    const groups = list.filter((row: any) => !row.groupNoiDungId).map((row: any) => {
      const groupKey = row.id || `G${Date.now()}_${groupIndex}`;
      groupMap[row.id] = groupKey;
      groupIndex++;
      return {
        key: groupKey,
        stt: String.fromCharCode(65 + groupIndex - 1),
        group: groupKey,
        isGroup: true,
        ngayBatDau: row.ngayBatDau || null,
        ngayKetThuc: row.ngayKetThuc || null,
        canhBaoTruocNgay: row.canhBaoTruocNgay,
        isKeHoachNoiBo: row.isKeHoachNoiBo,
        isCanhBao: row.isCanhBao,
        noiDungCongViec: row.noiDungCongViec,
        phanCong: row.phanCong,
      };
    });
    // Sau đó tạo các child, gán group là groupKey
    const childs = list.filter((row: any) => row.groupNoiDungId).map((row: any) => {
      const groupKey = groupMap[row.groupNoiDungId] || row.groupNoiDungId;
      return {
        key: row.id || `${row.stt}_${Math.random()}`,
        stt: row.stt,
        group: groupKey,
        isGroup: false,
        ngayBatDau: row.ngayBatDau || null,
        ngayKetThuc: row.ngayKetThuc || null,
        canhBaoTruocNgay: row.canhBaoTruocNgay,
        isKeHoachNoiBo: row.isKeHoachNoiBo,
        isCanhBao: row.isCanhBao,
        noiDungCongViec: row.noiDungCongViec,
        phanCong: row.phanCong,
      };
    });
    const mapped = [...groups, ...childs];
    setData(mapped);
    if (!mapped || mapped.length === 0) {
      const groupKey = `G${Date.now()}`;
      const newGroup = {
        key: groupKey,
        stt: 'A',
        group: groupKey,
        isGroup: true,
        ...DEFAULT_ROW,
      };
      setData([newGroup]);
      setEditingKey(groupKey);
      setEditingRow(newGroup);
    }
  }, [item]);

  // Đảm bảo luôn ở chế độ edit khi chỉ có 1 hàng nhóm mới
  useEffect(() => {
    if (
      data.length === 1 &&
      data[0].isGroup &&
      (!data[0].noiDungCongViec || data[0].noiDungCongViec.trim() === "") &&
      editingKey !== data[0].key
    ) {
      setEditingKey(data[0].key);
      setEditingRow(data[0]);
    }
  }, [data, editingKey]);

  // Helper: Create new group row
  const createGroupRow = useCallback((): RowType => {
    const groupKey = `G${Date.now()}`;
    const groupChar = String.fromCharCode(65 + data.filter(r => r.isGroup).length);
    return {
      key: groupKey,
      stt: groupChar,
      group: groupKey,
      isGroup: true,
      ...DEFAULT_ROW,
    };
  }, [data]);

  // Helper: Create new child row
  const createChildRow = useCallback((groupKey: string): RowType => {
    const stt = data.filter(r => r.group === groupKey && !r.isGroup).length + 1;
    return {
      key: `${groupKey}_${Date.now()}`,
      stt,
      group: groupKey,
      isGroup: false,
      ...DEFAULT_ROW,
    };
  }, [data]);

  // Hàm lưu hàng đang edit nếu có
  const saveEditingRowIfNeeded = () => {
    if (editingKey) {
      setData(prev => prev.map(row => row.key === editingKey ? { ...row, ...editingRow } as RowType : row));
      setEditingKey(null);
      setEditingRow({} as RowType);
    }
  };

  // Add group (hàm thường, không dùng useCallback)
  function handleAddGroup() {
    let newData = [...data];
    if (editingKey) {
      newData = newData.map(row => row.key === editingKey ? { ...row, ...editingRow } as RowType : row);
      setEditingKey(null);
      setEditingRow({} as RowType);
    }
    const newGroup = createGroupRow();
    newData.push(newGroup);
    setData(newData);
    setEditingKey(newGroup.key);
    setEditingRow(newGroup);
  }

  // Add child (hàm thường, không dùng useCallback)
  function handleAddChild(groupKey: string) {
    let newData = [...data];
    if (editingKey) {
      newData = newData.map(row => row.key === editingKey ? { ...row, ...editingRow } as RowType : row);
      setEditingKey(null);
      setEditingRow({} as RowType);
    }
    // Tìm vị trí group (so sánh đúng row.group)
    const groupIdx = newData.findIndex(r => r.isGroup && r.group === groupKey);
    let insertIdx = groupIdx + 1;
    for (let i = groupIdx + 1; i < newData.length; i++) {
      if (newData[i].group === groupKey && !newData[i].isGroup) {
        insertIdx = i + 1;
      } else if (newData[i].isGroup) {
        break;
      }
    }
    const newChild = createChildRow(groupKey);
    newData.splice(insertIdx, 0, newChild);
    setData(newData);
    setEditingKey(newChild.key);
    setEditingRow(newChild);
  }

  // Edit row
  const handleEditRow = useCallback((row: RowType) => {
    setEditingKey(row.key);
    setEditingRow({ ...row });
  }, []);

  // Delete row
  const handleDeleteRow = useCallback((key: string) => {
    setData(prev => {
      const newData = prev.filter(row => row.key !== key);
      // Nếu xoá hết thì tạo lại 1 nhóm mới ở chế độ edit
      if (newData.length === 0) {
        const groupChar = 'A';
        const groupKey = `G${Date.now()}`;
        const newGroup = {
          key: groupKey,
          stt: groupChar,
          group: groupChar,
          isGroup: true,
          ...DEFAULT_ROW,
        };
        setEditingKey(groupKey);
        setEditingRow(newGroup);
        return [newGroup];
      }
      // Nếu xoá hàng đang edit thì reset edit về null, trừ trường hợp còn 1 hàng nhóm mới thì giữ edit
      if (editingKey === key) {
        if (newData.length === 1 && newData[0].isGroup && (!newData[0].noiDungCongViec || newData[0].noiDungCongViec.trim() === "")) {
          setEditingKey(newData[0].key);
          setEditingRow(newData[0]);
        } else {
          setEditingKey(null);
          setEditingRow({} as RowType);
        }
      }
      return newData;
    });
  }, [editingKey]);

  // Save edit
  const handleSaveEdit = useCallback(() => {
    setData(prev => prev.map(row => row.key === editingKey ? { ...row, ...editingRow } as RowType : row));
    setEditingKey(null);
    setEditingRow({} as RowType);
  }, [editingKey, editingRow]);

  // Cancel edit
  const handleCancelEdit = useCallback(() => {
    setEditingKey(null);
    setEditingRow({} as RowType);
  }, []);

  // Drag & drop
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    const oldIndex = data.findIndex(item => item.key === active.id);
    const newIndex = data.findIndex(item => item.key === over.id);
    if (oldIndex === -1 || newIndex === -1) return;
    setData(arrayMove(data, oldIndex, newIndex));
  }, [data]);

  // Validate trước khi lưu
  const validateRows = () => {
    for (const row of data) {
      if (row.isGroup) {
        if (!row.noiDungCongViec || row.noiDungCongViec.trim() === "") {
          toast.error("Tên nhóm công việc không được để trống!");
          setEditingKey(row.key);
          setEditingRow(row);
          return false;
        }
      } else {
        if (!row.noiDungCongViec || row.noiDungCongViec.trim() === "") {
          toast.error("Tên công việc không được để trống!");
          setEditingKey(row.key);
          setEditingRow(row);
          return false;
        }
      }
    }
    return true;
  };

  // Tối ưu mapping khi lưu, đảm bảo đúng kiểu dữ liệu
  const handleSaveKeHoachTrienKhai = useCallback(async () => {
    if (!validateRows()) return;
    setLoading(true);
    try {
      const mappedData: DA_KeHoachThucHienCreateOrUpdateType[] = data.map(item => ({
        id: null,
        duAnId: idDuAn,
        group: item.group || null,
        isGroup: !!item.isGroup,
        stt: item.stt !== undefined && item.stt !== null ? String(item.stt) : null,
        ngayBatDau: !item.ngayBatDau || item.ngayBatDau === '' ? null : (dayjs.isDayjs(item.ngayBatDau) ? item.ngayBatDau.format("YYYY-MM-DD") : (dayjs(item.ngayBatDau, ["DD/MM/YYYY", "YYYY-MM-DD"], true).isValid() ? dayjs(item.ngayBatDau, ["DD/MM/YYYY", "YYYY-MM-DD"], true).format("YYYY-MM-DD") : null)),
        ngayKetThuc: !item.ngayKetThuc || item.ngayKetThuc === '' ? null : (dayjs.isDayjs(item.ngayKetThuc) ? item.ngayKetThuc.format("YYYY-MM-DD") : (dayjs(item.ngayKetThuc, ["DD/MM/YYYY", "YYYY-MM-DD"], true).isValid() ? dayjs(item.ngayKetThuc, ["DD/MM/YYYY", "YYYY-MM-DD"], true).format("YYYY-MM-DD") : null)),
        isKeHoachNoiBo: !!iskeHoachNoiBo,
        noiDungCongViec: item.noiDungCongViec || null,
        isCanhBao: !!item.isCanhBao,
        canhBaoTruocNgay: item.canhBaoTruocNgay ?? null,
        phanCong: item.phanCong || null,
      }));
      const response = await dA_KeHoachThucHienService.saveForm(mappedData);
      if (response && response.status) {
        toast.success("Lưu kế hoạch triển khai thành công!");
        if (onClose) onClose();
      } else {
        toast.error(response?.message || "Có lỗi xảy ra khi lưu!");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi lưu kế hoạch!");
    } finally {
      setLoading(false);
    }
  }, [data, idDuAn, iskeHoachNoiBo, onClose]);

  // Tách logic render row cho dễ bảo trì, truyền các handler và biến cần thiết vào
  const renderEditRow = (row: RowType, editingRow: RowType, setEditingRow: any, handleSaveEdit: any, handleCancelEdit: any, userOptions: any, styles: any) => (
    <DraggableRow row={row}>
      <td>
        <MenuOutlined style={{ cursor: "grab", color: "#999" }} />
      </td>
      <td style={{ textAlign: "center" }}>
        <Input
          value={editingRow.stt ?? 'A'}
          onChange={e => setEditingRow({ ...editingRow, stt: e.target.value })}
          size="small"
          style={{ width: 60, textAlign: "center" }}
        />
      </td>
      <td>
        <Input
          key="input-group"
          value={editingRow.noiDungCongViec || ""}
          onChange={e => setEditingRow({ ...editingRow, noiDungCongViec: e.target.value })}
          size="small"
          autoFocus={true}
          placeholder={editingRow.isGroup ? "Nhập tên nhóm" : "Nhập hạng mục công việc"}
        />
      </td>
      <td>
        <DatePicker.RangePicker
          value={[
            editingRow.ngayBatDau ? dayjs(editingRow.ngayBatDau, "DD/MM/YYYY") : undefined,
            editingRow.ngayKetThuc ? dayjs(editingRow.ngayKetThuc, "DD/MM/YYYY") : undefined,
          ]}
          onChange={dates => {
            setEditingRow({
              ...editingRow,
              ngayBatDau: dates && dates[0] && dayjs.isDayjs(dates[0]) ? dates[0].format("DD/MM/YYYY") : null,
              ngayKetThuc: dates && dates[1] && dayjs.isDayjs(dates[1]) ? dates[1].format("DD/MM/YYYY") : null,
            });
          }}
          format="DD/MM/YYYY"
          style={{ width: 220, height: 36, minHeight: 36, lineHeight: '36', boxSizing: 'border-box' }}
          size="small"
        />
      </td>
      {editingRow.isGroup ? (
        <>
          <td></td>
          <td></td>
        </>
      ) : (
        <>
          <td>
            <Form.Item name="phanCong" style={{ margin: 0 }}>
              <Select
                placeholder="Chọn người thực hiện"
                options={userOptions}
                allowClear
                showSearch
                optionFilterProp="label"
                value={editingRow.phanCong || ""}
                onChange={val => setEditingRow({ ...editingRow, phanCong: val })}
                style={{ width: 200, height: 38, minHeight: 38, lineHeight: '38', top:10, boxSizing: 'border-box' }}
                size="small"
                className={styles.inputAssign}
              />
            </Form.Item>
          </td>
          <td style={{ textAlign: "center" }}>
            <Checkbox
              checked={editingRow.isCanhBao || false}
              onChange={e => setEditingRow({ ...editingRow, isCanhBao: e.target.checked })}
            />
          </td>
        </>
      )}
      <td style={{ textAlign: "center" }}>
        <Button
          icon={<SaveOutlined />}
          type="link"
          onClick={handleSaveEdit}
          style={{ padding: 0, marginRight: 8 }}
        />
        <Button
          icon={<CloseOutlined />}
          type="link"
          onClick={handleCancelEdit}
          style={{ padding: 0 }}
        />
      </td>
    </DraggableRow>
  );

  const renderGroupRow = (row: RowType, handleAddChild: any, handleEditRow: any, handleDeleteRow: any, styles: any, userOptions: any) => (
    <DraggableRow row={row}>
      <td>
        <MenuOutlined style={{ cursor: "grab", color: "#999" }} />
      </td>
      <td style={{ textAlign: "center", fontWeight: "bold" }}>{row.stt}</td>
      <td style={{ fontWeight: "bold" }}>{row.noiDungCongViec}</td>
      <td colSpan={3} style={{ fontWeight: "bold" }}>
        {/* Nếu nhóm không có ngày, tự động lấy min ngày bắt đầu và max ngày kết thúc của các child thuộc group */}
        {(() => {
          // Ưu tiên hiển thị ngày của nhóm nếu có
          if (
            row.ngayBatDau &&
            row.ngayKetThuc &&
            dayjs(row.ngayBatDau, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid() &&
            dayjs(row.ngayKetThuc, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()
          ) {
            return `${dayjs(row.ngayBatDau, ["YYYY-MM-DD", "DD/MM/YYYY"]).format("DD/MM/YYYY")} - ${dayjs(row.ngayKetThuc, ["YYYY-MM-DD", "DD/MM/YYYY"]).format("DD/MM/YYYY")}`;
          }
          // Nếu không có ngày nhóm, tổng hợp từ child
          const childDates = data.filter(r => r.group === row.group && !r.isGroup);
          if (childDates.length === 0) return "-";
          const validStarts = childDates.map(r => r.ngayBatDau).filter(d => d && dayjs(d, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()).map(d => dayjs(d, ["YYYY-MM-DD", "DD/MM/YYYY"]));
          const validEnds = childDates.map(r => r.ngayKetThuc).filter(d => d && dayjs(d, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()).map(d => dayjs(d, ["YYYY-MM-DD", "DD/MM/YYYY"]));
          if (validStarts.length === 0 && validEnds.length === 0) return "-";
          const minStart = validStarts.length > 0 ? dayjs(validStarts.reduce((a, b) => a.isBefore(b) ? a : b)).format("DD/MM/YYYY") : "";
          const maxEnd = validEnds.length > 0 ? dayjs(validEnds.reduce((a, b) => a.isAfter(b) ? a : b)).format("DD/MM/YYYY") : "";
          return `${minStart}${minStart && maxEnd ? ' - ' : ''}${maxEnd}`;
        })()}
      </td>
      <td style={{ textAlign: "center" }}>
        <Button
          icon={<PlusOutlined />}
          onClick={() => handleAddChild(row.group!)}
          type="link"
          size="small"
          className={styles.btnThem}
        />
        <br />
        <Button
          icon={<EditOutlined />}
          type="link"
          onClick={() => handleEditRow(row)}
          style={{ padding: 0, marginRight: 8 }}
        />
        <Button
          icon={<DeleteOutlined />}
          type="link"
          danger
          onClick={() => {
            Modal.confirm({
              title: 'Bạn có chắc chắn muốn xoá?',
              content: 'Hành động này không thể hoàn tác.',
              okText: 'Xoá',
              okType: 'danger',
              cancelText: 'Huỷ',
              onOk: () => handleDeleteRow(row.key),
            });
          }}
          style={{ padding: 0 }}
        />
      </td>
    </DraggableRow>
  );

  const renderChildRow = (row: RowType, handleEditRow: any, handleDeleteRow: any, userOptions: any[], styles: any) => (
    <DraggableRow row={row}>
      <td>
        <MenuOutlined style={{ cursor: "grab", color: "#999" }} />
      </td>
      <td style={{ textAlign: "center" }}>{row.stt}</td>
      <td>{row.noiDungCongViec}</td>
      <td>
        {(row.ngayBatDau && dayjs(row.ngayBatDau, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()
          ? dayjs(row.ngayBatDau, ["YYYY-MM-DD", "DD/MM/YYYY"]).format("DD/MM/YYYY")
          : "")}
        {row.ngayBatDau && row.ngayKetThuc ? " - " : ""}
        {(row.ngayKetThuc && dayjs(row.ngayKetThuc, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()
          ? dayjs(row.ngayKetThuc, ["YYYY-MM-DD", "DD/MM/YYYY"]).format("DD/MM/YYYY")
          : "")}
      </td>
      <td>{userOptions.find((u: any) => u.value === row.phanCong)?.label || ""}</td>
      <td style={{ textAlign: "center" }}>
        {row.isCanhBao ? (
          <Checkbox checked disabled />
        ) : (
          <Checkbox disabled />
        )}
      </td>
      <td style={{ textAlign: "center" }}>
        <Button
          icon={<EditOutlined />}
          type="link"
          onClick={() => handleEditRow(row)}
          style={{ padding: 0, marginRight: 8 }}
        />
        <Button
          icon={<DeleteOutlined />}
          type="link"
          danger
          onClick={() => {
            Modal.confirm({
              title: 'Bạn có chắc chắn muốn xoá?',
              content: 'Hành động này không thể hoàn tác.',
              okText: 'Xoá',
              okType: 'danger',
              cancelText: 'Huỷ',
              onOk: () => handleDeleteRow(row.key),
            });
          }}
          style={{ padding: 0 }}
        />
      </td>
    </DraggableRow>
  );

  // Render
  return (
    <Drawer
      open={true}
      onClose={onClose}
      className="customDrawer"
      width="85%"
      title="Kế hoạch triển khai nội bộ"
      destroyOnClose
      placement="right"
    >
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={data.map(item => item.key)}
          strategy={verticalListSortingStrategy}
        >
          <table className={styles.customTable}>
            <thead>
              <tr>
                <th style={{ width: 32 }}></th>
                <th style={{ width: 60 }}>STT</th>
                <th>Hạng mục công việc</th>
                <th>Thời gian thực hiện</th>
                <th>Assign</th>
                <th>Cảnh báo?</th>
                <th style={{ width: 120, textAlign: "center" }}>
                  <Button
                    onClick={handleAddGroup}
                    type="dashed"
                    size="small"
                    className={styles.btnThem}
                  >
                    Thêm nhóm
                  </Button>
                </th>
              </tr>
            </thead>
            <tbody>
              {data.map((row) => (
                <React.Fragment key={row.key}>
                  {editingKey === row.key
                    ? renderEditRow(row, editingRow, setEditingRow, handleSaveEdit, handleCancelEdit, userOptions, styles)
                    : row.isGroup
                    ? renderGroupRow(row, handleAddChild, handleEditRow, handleDeleteRow, styles, userOptions)
                    : renderChildRow(row, handleEditRow, handleDeleteRow, userOptions, styles)}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </SortableContext>
      </DndContext>
      <div style={{ textAlign: "right", marginTop: 16 }}>
        <Button type="primary" onClick={handleSaveKeHoachTrienKhai} loading={loading} disabled={loading}>
          Hoàn thành
        </Button>
      </div>
    </Drawer>
  );
};

export default KeHoachTrienKhaiCustomTable;