import { useRef, useState, KeyboardEvent, ChangeEvent, DragEvent } from "react";
import { AiOutlineCloudUpload } from "react-icons/ai";
import UploadItem from "./UploadItem";
import { useFileNavigation } from "@/contexts/FileNavigationContext";
import { useFiles } from "@/contexts/FilesContext";
import { getFileExtension } from "@/utils/fileManagerUtils/getFileExtension";
import { getDataSize } from "@/utils/fileManagerUtils/getDataSize";
import Button from "@/components/fileManager-components/Button/Button";
import Loader from "@/components/fileManager-components/Loader/Loader";
import { FileDataType, FileManagerType } from "@/types/fileManager/fileManager";

// Types
type UploadFileActionProps = {
  fileUploadConfig: any;
  maxFileSize?: number;
  acceptedFileTypes?: string[];
  onFileUploading: (file: File, currentFolder: FileManagerType) => any;
  onFileUploaded: (response: any) => void;
};

const UploadFileAction = ({
  fileUploadConfig,
  maxFileSize,
  acceptedFileTypes,
  onFileUploading,
  onFileUploaded,
}: UploadFileActionProps) => {
  const [files, setFiles] = useState<FileDataType[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState<Record<string, boolean>>({});
  const { currentFolder, currentPathFiles } = useFileNavigation();
  const { onError } = useFiles();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleChooseFileKeyDown = (e: KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === "Enter" && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const checkFileError = (file: File): string | undefined => {
    if (acceptedFileTypes) {
      const extError = !acceptedFileTypes.includes(getFileExtension(file.name));
      if (extError) return "Định dạng tài liệu không được cho phép.";
    }

    const fileExists = currentPathFiles.some(
      (item: any) => item.name.toLowerCase() === file.name.toLowerCase() && !item.isDirectory
    );
    if (fileExists) return "Tài liệu đã tồn tại.";

    const sizeError = maxFileSize && file.size > maxFileSize;
    if (sizeError) return `Kích thước tối đa cho phép là ${getDataSize(maxFileSize, 0)}.`;
  };

  const setSelectedFiles = (selectedFiles: File[]) => {
    selectedFiles = selectedFiles.filter(
      (item) =>
        !files.some((fileData) => fileData.file.name.toLowerCase() === item.name.toLowerCase())
    );

    if (selectedFiles.length > 0) {
      const newFiles: FileDataType[] = selectedFiles.map((file) => {
        const appendData = onFileUploading(file, currentFolder);
        const error = checkFileError(file);
        if (error) {
          // onError({ type: "upload", message: error }, file);
          onError({ type: "upload", message: error });
        }
        return {
          file,
          appendData,
          ...(error && { error }),
        };
      });
      setFiles((prev) => [...prev, ...newFiles]);
    }
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFiles = Array.from(e.dataTransfer.files);
    setSelectedFiles(droppedFiles);
  };

  const handleChooseFile = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const choosenFiles = Array.from(e.target.files);
      setSelectedFiles(choosenFiles);
    }
  };

  const handleFileRemove = (index: number) => {
    setFiles((prev) => {
      const newFiles = prev.map((file, i) => {
        if (index === i) {
          return {
            ...file,
            removed: true,
          };
        }
        return file;
      });

      if (newFiles.every((file) => !!file.removed)) return [];

      return newFiles;
    });
  };

  return (
    <div className={`fm-upload-file ${files.length > 0 ? "file-selcted" : ""}`}>
      <div className="select-files">
        <div
          className={`draggable-file-input ${isDragging ? "dragging" : ""}`}
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
          onDragEnter={() => setIsDragging(true)}
          onDragLeave={() => setIsDragging(false)}
        >
          <div className="input-text">
            <AiOutlineCloudUpload size={30} />
            <span>Kéo và thả tài liệu vào đây để tải lên</span>
          </div>
        </div>
        <div className="btn-choose-file">
          <Button padding="0" onKeyDown={handleChooseFileKeyDown}>
            <label htmlFor="chooseFile">Chọn tài liệu</label>
            <input
              ref={fileInputRef}
              type="file"
              id="chooseFile"
              className="choose-file-input"
              onChange={handleChooseFile}
              multiple
              accept={acceptedFileTypes?.join(",")}
            />
          </Button>
        </div>
      </div>
      {files.length > 0 && (
        <div className="files-progress">
          <div className="heading">
            {Object.values(isUploading).some((fileUploading) => fileUploading) ? (
              <>
                <h2>Đang tải lên</h2>
                <Loader loading={true} className="upload-loading" />
              </>
            ) : (
              <h2>Hoàn tất</h2>
            )}
          </div>
          <ul>
            {files.map((fileData, index) => (
              <UploadItem
                index={index}
                key={index}
                fileData={fileData}
                setFiles={setFiles}
                fileUploadConfig={fileUploadConfig}
                setIsUploading={setIsUploading}
                onFileUploaded={onFileUploaded}
                handleFileRemove={handleFileRemove}
              />
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default UploadFileAction;
