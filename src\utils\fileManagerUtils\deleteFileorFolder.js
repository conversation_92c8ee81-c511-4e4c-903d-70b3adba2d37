export const deleteFileAndChildren = (fileId, allFiles) => {
    // T<PERSON><PERSON> tất cả các file con
    const children = allFiles.filter(file => file.parentId === fileId);

    // <PERSON><PERSON> quy xóa từng file con
    for (const child of children) {
        allFiles = deleteFileAndChildren(child.id, allFiles);
    }

    // X<PERSON>a ch<PERSON>h nó
    return allFiles.filter(file => file.id !== fileId);
}
