.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e6f0fa 100%);
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(22,119,255,0.07);
  padding: 32px 24px 24px 24px;
  max-width: 440px;
  width: 100%;
  border: 1.5px solid #e6f0fa;
}

.uploadBtn {
  width: 160px;
  height: 44px;
  font-weight: 600;
  font-size: 16px;
  border-radius: 10px;
  background: #f5f7fa;
  color: #1677ff;
  border: 1.5px solid #91caff;
  transition: background 0.2s, color 0.2s, border 0.2s;
  box-shadow: 0 2px 8px rgba(22,119,255,0.04);
}
.uploadBtn:hover {
  background: #e6f4ff;
  color: #fff;
  border-color: #1677ff;
}

.fileList {
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
  min-height: 32px;
}
.fileItem {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 8px;
  padding: 10px 16px;
  margin-bottom: 10px;
  font-size: 15px;
  box-shadow: 0 1px 4px rgba(22,119,255,0.04);
  border: 1px solid #e6f0fa;
  transition: background 0.2s, box-shadow 0.2s;
}
.fileItem:hover {
  background: #e6f4ff;
  box-shadow: 0 2px 8px rgba(22,119,255,0.08);
}
.fileName {
  flex: 1;
  word-break: break-all;
  color: #222;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  font-size: 13px;
}
.removeBtn {
  margin-left: 12px;
  color: #ff4d4f !important;
  font-weight: 600;
  border-radius: 8px;
  transition: background 0.2s;
  padding: 0 10px;
}
.removeBtn:hover {
  background: #fff1f0;
  color: #d32029 !important;
}

.saveBtn {
  width: 160px;
  height: 44px;
  font-weight: 600;
  font-size: 16px;
  border-radius: 10px;
  background: #1677ff;
  color: #fff;
  border: none;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 2px 8px rgba(22,119,255,0.08);
}
.saveBtn:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
}
.saveBtn:not(:disabled):hover {
  background: #0958d9;
  color: #fff;
}

@media (max-width: 600px) {
  .container {
    max-width: 100%;
    padding: 16px 6px 12px 6px;
  }
  .uploadBtn, .saveBtn {
    width: 100%;
    min-width: 0;
    font-size: 15px;
    height: 40px;
  }
  .fileItem {
    font-size: 14px;
    padding: 8px 8px;
  }
}
