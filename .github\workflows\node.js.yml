name: Build

on:
  push:
    branches:
      - production

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install dependencies
        run: npm install

      - name: Build project
        run: npm run build

      - name: Create script.bat
        run: |
          echo @echo off > script.bat
          echo "C:\Program Files\7-Zip\7z.exe" x build.zip -y >> script.bat
          echo npm install >> script.bat
          echo pm2 restart thanhtra_bqp >> script.bat

      - name: Zip build folder
        run: zip -r build.zip . -x "node_modules/*" -x ".git/*"

      - name: Upload build to VPS
        uses: appleboy/scp-action@v0.1.4
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_KEY }}
          source: "build.zip"
          target: ${{ secrets.VPS_DIR }}

      - name: Remote SSH - Unzip and <PERSON>art
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_KEY }}
          script: |
            cd ${{ secrets.VPS_DIR }}
            mkdir test
            "C:\Program Files\7-Zip\7z.exe" x build.zip -y
            npm install
            pm2 restart thanhtra_bqp
