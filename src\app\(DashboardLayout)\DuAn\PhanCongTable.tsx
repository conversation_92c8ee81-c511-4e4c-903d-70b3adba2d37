import React from "react";
import { <PERSON>, <PERSON><PERSON>, Select, Popconfirm } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { DropdownOption } from "@/types/general";
import { userService } from "@/services/user/user.service";
import { roleService } from "@/services/role/role.service";
import { DA_PhanCongCreateOrUpdateType } from "@/types/dA_DuAn/dA_PhanCong";

interface PhanCongTableProps {
  phanCongList: DA_PhanCongCreateOrUpdateType[];
  userOptions: DropdownOption[];
  roleOptions: DropdownOption[];
  onAdd: () => void;
  onChange: (index: number, key: "userId" | "vaiTroId", value: string) => void;
  onDelete: (index: number) => void;
}

const PhanCongTable: React.FC<PhanCongTableProps> = ({
  phanCongList,
  userOptions,
  roleOptions,
  onAdd,
  onChange,
  onDelete,
}) => {
  const columns = [
    {
      title: "STT",
      dataIndex: "stt",
      width: 60,
      align: "center" as const,
      render: (_: any, __: any, idx: number) => idx + 1,
    },
    {
      title: "Thành viên",
      dataIndex: "userId",
      render: (_: any, record: any, idx: number) => (
        <Select
          showSearch
          placeholder="Chọn thành viên"
          value={phanCongList[idx]?.userId}
          options={userOptions.map(u => ({ label: u.label, value: u.value }))}
          onChange={val => onChange(idx, "userId", val)}
          style={{ width: "100%" }}
          optionFilterProp="label"
        />
      ),
    },
    {
      title: "Vai trò",
      dataIndex: "roleId",
      render: (_: any, record: any, idx: number) => (
        <Select
          showSearch
          placeholder="Chọn vai trò"
          value={phanCongList[idx]?.vaiTroId}
          options={roleOptions.map(r => ({ label: r.label, value: r.value }))}
          onChange={val => onChange(idx, "vaiTroId", val)}
          style={{ width: "100%" }}
          optionFilterProp="label"
        />
      ),
    },
    {
      title: "Thao tác",
      dataIndex: "actions",
      align: "center" as const,
      width: 80,
      render: (_: any, __: any, idx: number) => (
        <Popconfirm title="Xoá thành viên này?" onConfirm={() => onDelete(idx)}>
          <Button danger icon={<DeleteOutlined />} size="small" />
        </Popconfirm>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Button
        type="dashed"
        icon={<PlusOutlined />}
        onClick={onAdd}
        style={{ marginBottom: 12 }}
      >
        Thêm thành viên
      </Button>
      <Table
        columns={columns}
        dataSource={phanCongList}
        rowKey={(_, idx) => String(idx)}
        pagination={false}
        bordered
        size="small"
      />
    </div>
  );
};

export default PhanCongTable;