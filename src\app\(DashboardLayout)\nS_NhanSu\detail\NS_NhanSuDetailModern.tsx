"use client";
import React, { useEffect, useState } from "react";
import { Card, Row, Col, Descriptions, Button, Modal, message } from "antd";
import {
  UserOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  IdcardOutlined,
  BankOutlined,
  CalendarOutlined,
  ManOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import GioiTinhConstant from "@/constants/QLNhanSu/GioiTinhConstant";
import { NS_NhanSuType } from "@/types/nS_NhanSu/nS_NhanSu";
import { duLieuDanhMucService } from "@/services/duLieuDanhMuc/duLieuDanhMuc.service";
import { departmentService } from "@/services/department/department.service";
import ChangeAvatar from "./changeAvatarNhanSu";
import nS_NhanSuService from "@/services/nS_NhanSu/nS_NhanSuService";
interface Props {
  item?: NS_NhanSuType | null;
  onClose: () => void;
}
const StaticFileUrl = process.env.NEXT_PUBLIC_STATIC_FILE_BASE_URL;

const NS_NhanSuDetailModern: React.FC<Props> = ({ item, onClose }) => {
  const [chucVu, setChucVu] = useState<string>("");
  const [phongBan, setPhongBan] = useState<string>("");
  const [isOpenModalAvatar, setIsOpenModalAvatar] = useState<boolean>(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [img, setImg] = useState<string | undefined>();
  const [userInfo, getUserInfo] = useState<NS_NhanSuType>();

  const handleShowModalAvatar = () => {
    setIsOpenModalAvatar(true);
  };
  const handleCloseAvatar = () => {
    setIsOpenModalAvatar(false);
  };
  const hanleEditAvatarSuccess = () => {
    setIsOpenModalAvatar(false);
    messageApi.success("Cập nhật thành công!");
    loadData();
  };

  useEffect(() => {
    const fetchDropdowns = async () => {
      try {
        if (item) {
          if (item.chucVuId) {
            const chucVuData = await duLieuDanhMucService.GetById(
              item.chucVuId
            );
            setChucVu(chucVuData?.data?.name ?? "");
          }
          if (item.phongBanId) {
            const phongBanData = await departmentService.getDetail(
              item.phongBanId
            );
            setPhongBan(phongBanData?.data?.name ?? "");
          }
        }
      } catch (error) {
        message.error("Lỗi khi tải dữ liệu chức vụ/phòng ban");
      }
    };
    fetchDropdowns();
  }, [item]);

  const loadData = async () => {
    const response = await nS_NhanSuService.getDetail(item?.id ?? "");
    if (response.status) {
      getUserInfo(response.data);
    }
  };
  useEffect(() => {
    loadData();
  }, []);

  return (
    <>
      {contextHolder}
      <Modal
        title="Chi tiết nhân sự"
        open={true}
        onCancel={onClose}
        footer={[
          <Button key="close" onClick={onClose}>
            Đóng
          </Button>,
        ]}
        width={800}
        bodyStyle={{ padding: 0 }}
      >
        <Card bordered={false} style={{ padding: 24, borderRadius: 12 }}>
          <Row gutter={[24, 12]} align="top">
            <Col
              xs={24}
              sm={8}
              md={7}
              lg={6}
              xl={6}
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                minHeight: 220,
              }}
            >
              <div
                style={{
                  width: 120,
                  height: 120,
                  borderRadius: "50%",
                  overflow: "hidden",
                  marginBottom: 12,
                  background: "#f3f4f6",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <img
                  src={
                    userInfo?.hinhAnh
                      ? `${StaticFileUrl}${userInfo.hinhAnh}?v=${Date.now()}`
                      : `/img/avatars/1.jpg`
                  }
                  alt="avatar"
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }}
                />
              </div>
              <Button
                type="primary"
                size="small"
                style={{ marginTop: 8 }}
                onClick={handleShowModalAvatar}
              >
                Thay đổi ảnh đại diện
              </Button>
              {isOpenModalAvatar && (
                <ChangeAvatar
                  id={item?.id ?? ""}
                  onSuccess={hanleEditAvatarSuccess}
                  onClose={handleCloseAvatar}
                />
              )}
            </Col>
            <Col xs={24} sm={16} md={17} lg={18} xl={18}>
              <Descriptions
                bordered
                column={1}
                size="small"
                style={{ background: "#fff", borderRadius: 8, padding: 12 }}
                labelStyle={{ width: 160, fontWeight: 500, background: "#fff" }}
                contentStyle={{ background: "#fff" }}
              >
                <Descriptions.Item
                  label={
                    <>
                      <UserOutlined /> Họ tên
                    </>
                  }
                >
                  {item?.hoTen || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <IdcardOutlined /> CMND
                    </>
                  }
                >
                  {item?.cMND || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <InfoCircleOutlined /> Chức vụ
                    </>
                  }
                >
                  {chucVu || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <InfoCircleOutlined /> Phòng ban
                    </>
                  }
                >
                  {phongBan || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <CalendarOutlined /> Ngày sinh
                    </>
                  }
                >
                  {item?.ngaySinh
                    ? dayjs(item.ngaySinh).format("DD/MM/YYYY")
                    : "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <CalendarOutlined /> Ngày vào làm
                    </>
                  }
                >
                  {item?.ngayVaoLam
                    ? dayjs(item.ngayVaoLam).format("DD/MM/YYYY")
                    : "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <ManOutlined /> Giới tính
                    </>
                  }
                >
                  {GioiTinhConstant.getDisplayName(item?.gioiTinh ?? 0)}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <PhoneOutlined /> Điện thoại
                    </>
                  }
                >
                  {item?.dienThoai || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <MailOutlined /> Email
                    </>
                  }
                >
                  {item?.email || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <HomeOutlined /> Địa chỉ thường trú
                    </>
                  }
                >
                  {item?.diaChiThuongTru || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <HomeOutlined /> Địa chỉ tạm trú
                    </>
                  }
                >
                  {item?.diaChiTamTru || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <IdcardOutlined /> Nơi cấp CMND
                    </>
                  }
                >
                  {item?.noiCapCMND || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <IdcardOutlined /> Mã số thuế cá nhân
                    </>
                  }
                >
                  {item?.maSoThueCaNhan || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <BankOutlined /> Số tài khoản ngân hàng
                    </>
                  }
                >
                  {item?.soTaiKhoanNganHang || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <BankOutlined /> Ngân hàng
                    </>
                  }
                >
                  {item?.nganHang || "Chưa cập nhật"}
                </Descriptions.Item>
                <Descriptions.Item
                  label={
                    <>
                      <InfoCircleOutlined /> Trạng thái
                    </>
                  }
                >
                  {Number(item?.trangThai) === 1
                    ? "Đang làm việc"
                    : "Đã nghỉ việc"}
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </Card>
      </Modal>
    </>
  );
};

export default NS_NhanSuDetailModern;
