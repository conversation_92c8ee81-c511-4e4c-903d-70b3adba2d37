import React from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Form, Input, Select, DatePicker, Row } from "antd";
import { DownloadOutlined, SearchOutlined } from "@ant-design/icons";
import Flex from "@/components/shared-components/Flex";
import { downloadFileFromBase64 } from "@/utils/fileDownload";
import { useForm } from "antd/es/form/Form";
import { toast } from "react-toastify";
import * as extensions from "@/utils/extensions";

import { EmailThongBaoSearchType } from "@/types/emailThongBao/emailThongBao";
import emailThongBaoService from "@/services/emailThongBao/emailThongBaoService";

interface SearchProps {
  onFinish: ((values: EmailThongBaoSearchType) => void) | undefined;
  pageIndex: number;
  pageSize: number;
}
const Search: React.FC<SearchProps> = ({ onFinish, pageIndex, pageSize }) => {
  const [form] = useForm<EmailThongBaoSearchType>();

  const Export = async () => {
    const formValues = form.getFieldsValue();

    const exportData = {
      ...formValues,
      pageIndex,
      pageSize,
    };

    const response = await emailThongBaoService.exportExcel(exportData);
    if (response.status) {
      downloadFileFromBase64(response.data, "Danh sách email thông báo.xlsx");
    } else {
      toast.error(response.message);
    }
  };

  return (
    <>
      <Card className="customCardShadow mb-3">
        <Form
          form={form}
          layout="vertical"
          name="basic"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          initialValues={{ remember: true }}
          onFinish={onFinish}
          autoComplete="off"
        >
          <Row gutter={24}>
            <Col xl={12} lg={12} md={12} xs={24}>
              <Form.Item<EmailThongBaoSearchType> key="ma" label="Mã" name="ma">
                <Input placeholder="Mã" />
              </Form.Item>
            </Col>
            {/* <Col xl={6} lg={8} md={12} xs={24}>
							<Form.Item<EmailThongBaoSearchType>
								key="noiDung"
								label="Nội dung"
								name="noiDung">
								<Input placeholder="Nội dung"/>
							</Form.Item>
						</Col> */}
          </Row>

          <Flex
            alignItems="center"
            justifyContent="center"
            className="btn-group"
          >
            <Button
              type="primary"
              htmlType="submit"
              icon={<SearchOutlined />}
              size="small"
            >
              Tìm kiếm
            </Button>
            {/* <Button
              onClick={Export}
              type="primary"
              icon={<DownloadOutlined />}
              className="colorKetXuat"
              size="small"
            >
              Kết xuất
            </Button> */}
          </Flex>
        </Form>
      </Card>
    </>
  );
};

export default Search;
