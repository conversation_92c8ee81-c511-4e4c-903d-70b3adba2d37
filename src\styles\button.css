/* Button Base Styles */
.custom-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s;
    height: auto;
    font-size: 0.95rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.08);
    border: none;
}

.custom-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px -1px rgba(0, 0, 0, 0.12);
    filter: brightness(1.05);
}

.custom-btn:active {
    transform: translateY(1px);
}

/* Primary Button - Blue (Default Confirm) */
.custom-btn-primary {
    background: linear-gradient(to right, #2563eb, #1e40af);
    color: white;
    border: 1px solid transparent;
}

.custom-btn-primary:hover {
    background: linear-gradient(to right, #1d4ed8, #1e3a8a);
    color: white;
    border-color: rgba(255, 255, 255, 0.2);
}

/* Success Button - Green */
.custom-btn-success {
    background: linear-gradient(to right, #22c55e, #16a34a);
    color: white;
}

/* Danger Button - Red */
.custom-btn-danger {
    background: linear-gradient(to right, #ef4444, #dc2626);
    color: white;
}

/* Warning Button - Orange/Yellow */
.custom-btn-warning {
    background: linear-gradient(to right, #f97316, #ea580c);
    color: white;
}

/* Info Button - Light Blue */
.custom-btn-info {
    background: linear-gradient(to right, #0ea5e9, #0284c7);
    color: white;
}

/* Navigation Button (for menu toggle) */
.custom-btn-nav {
    background: linear-gradient(to right, #2563eb, #1e40af);
    color: white;
    padding: 0.625rem;
}

.custom-btn-nav:hover {
    background: linear-gradient(to right, #1d4ed8, #1e3a8a);
}

/* Secondary Button - Gray */
.custom-btn-secondary {
    background: linear-gradient(to right, #64748b, #475569);
    color: white;
}

/* Outline Button Styles */
.custom-btn-outline {
    background: transparent;
    border: 1px solid;
    box-shadow: none;
}

.custom-btn-outline-primary {
    border-color: #2563eb;
    color: #2563eb;
}

.custom-btn-outline-primary:hover {
    background-color: rgba(37, 99, 235, 0.1);
}

/* Ghost Button */
.custom-btn-ghost {
    background: transparent;
    color: #2563eb;
    box-shadow: none;
}

.custom-btn-ghost:hover {
    background-color: rgba(37, 99, 235, 0.1);
}

/* Size variants */
.custom-btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.custom-btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* Full width */
.custom-btn-block {
    display: flex;
    width: 100%;
}

/* Icon button styles */
.custom-btn-icon {
    padding: 0.5rem;
    border-radius: 9999px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Disabled state */
.custom-btn:disabled, .custom-btn.disabled {
    opacity: 0.65;
    cursor: not-allowed;
    pointer-events: none;
}

/* Add these overrides at the end of your button.css file */
button.ant-btn.custom-btn.custom-btn-info,
button.ant-btn.custom-btn.custom-btn-primary,
button.ant-btn.custom-btn.custom-btn-success,
button.ant-btn.custom-btn.custom-btn-danger,
button.ant-btn.custom-btn.custom-btn-warning,
button.ant-btn.custom-btn.custom-btn-secondary,
button.ant-btn.custom-btn.custom-btn-nav {
    /* Explicitly use !important to ensure our styles take precedence */
    background-image: linear-gradient(to right, #0ea5e9, #0284c7) !important;
}

button.ant-btn.custom-btn.custom-btn-primary {
    background-image: linear-gradient(to right, #2563eb, #1e40af) !important;
}

button.ant-btn.custom-btn.custom-btn-success {
    background-image: linear-gradient(to right, #22c55e, #16a34a) !important;
}

button.ant-btn.custom-btn.custom-btn-danger {
    background-image: linear-gradient(to right, #ef4444, #dc2626) !important;
}

button.ant-btn.custom-btn.custom-btn-warning {
    background-image: linear-gradient(to right, #f97316, #ea580c) !important;
}

button.ant-btn.custom-btn.custom-btn-secondary {
    background-image: linear-gradient(to right, #64748b, #475569) !important;
}

button.ant-btn.custom-btn.custom-btn-nav {
    background-image: linear-gradient(to right, #2563eb, #1e40af) !important;
}

/* Handle hover states with explicit gradients */
button.ant-btn.custom-btn.custom-btn-info:hover {
    background-image: linear-gradient(to right, #0284c7, #0369a1) !important;
}

button.ant-btn.custom-btn.custom-btn-primary:hover {
    background-image: linear-gradient(to right, #1d4ed8, #1e3a8a) !important;
}

button.ant-btn.custom-btn.custom-btn-success:hover {
    background-image: linear-gradient(to right, #16a34a, #15803d) !important;
}

button.ant-btn.custom-btn.custom-btn-danger:hover {
    background-image: linear-gradient(to right, #dc2626, #b91c1c) !important;
}

button.ant-btn.custom-btn.custom-btn-warning:hover {
    background-image: linear-gradient(to right, #ea580c, #c2410c) !important;
}

button.ant-btn.custom-btn.custom-btn-secondary:hover {
    background-image: linear-gradient(to right, #475569, #334155) !important;
}

button.ant-btn.custom-btn.custom-btn-nav:hover {
    background-image: linear-gradient(to right, #1d4ed8, #1e3a8a) !important;
}

/* For outline buttons, maintain transparency */
button.ant-btn.custom-btn.custom-btn-outline,
button.ant-btn.custom-btn.custom-btn-outline-primary,
button.ant-btn.custom-btn.custom-btn-ghost {
    background: transparent !important;
    background-image: none !important;
}

button.ant-btn.custom-btn.custom-btn-outline:hover,
button.ant-btn.custom-btn.custom-btn-outline-primary:hover,
button.ant-btn.custom-btn.custom-btn-ghost:hover {
    background-color: rgba(37, 99, 235, 0.1) !important;
    background-image: none !important;
}