@import "../../../../styles/variables";

.files {
  position: relative;
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  column-gap: 0.5em;
  row-gap: 5px;
  height: calc(100% - (35px + 16px));
  @include overflow-y-scroll;
  padding: 8px;
  padding-right: 4px;

  .drag-move-tooltip {
    background-color: white;
    font-size: .78em;
    position: fixed;
    text-wrap: nowrap;
    border: 1px dashed black;
    padding: 1px 5px 2px 24px;
    border-radius: 3px;
    font-weight: bold;
    color: var(--file-manager-primary-color);
    z-index: 2;

    .drop-zone-file-name {
      color: rgb(48, 48, 48);
    }
  }

  .file-item-container {
    border-radius: 5px;
    margin: 5px 0;

    .drag-icon {
      position: absolute !important;
      top: -1000px;
      left: -1000px;
      z-Index: -1;
      border-radius: 4px;
      position: relative;
      color: white;
      background-color: var(--file-manager-primary-color);
      padding: 3px 8px;
    }
  }

  .file-item-container.file-drop-zone {
    background-color: rgb(0, 0, 0, 0.08) !important;
  }

  .file-item {
    position: relative;
    height: 81px;
    width: 138px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
    justify-content: space-between;
    padding-top: 13px;
    padding-bottom: 1px;
    border-radius: 5px;

    &:hover {
      background-color: $item-hover-color;
    }

    .selection-checkbox {
      position: absolute;
      left: 5px;
      top: 8px;
    }

    .hidden {
      visibility: hidden;
    }

    .visible {
      visibility: visible;
    }

    .rename-file-container {
      position: absolute;
      top: 65px;
      width: 100%;
      text-align: center;
    }

    .rename-file-container.list {
      top: 6px;
      left: 58px;
      text-align: left;

      .rename-file {
        min-width: 15%;
        text-align: left;
        border-radius: 3px;
        border: none;
        top: 5px;
        white-space: nowrap;
        overflow-x: hidden;
        max-width: calc(100% - 62px);
      }

      .folder-name-error.right {
        left: 0px;
        bottom: -72px;
      }
    }

    .file-name {
      max-width: 115px;
    }
  }

  .file-selected {
    background-color: var(--file-manager-primary-color);
    color: white;

    &:hover {
      background-color: var(--file-manager-primary-color);
    }
  }

  .file-moving {
    opacity: 0.5;
  }
}

.files.list {
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 0;
  padding-left: 0px;
  padding-top: 0px;

  .files-header {
    font-size: 0.83em;
    font-weight: 600;
    display: flex;
    gap: 5px;
    border-bottom: 1px solid #dddddd;
    padding: 4px 0 4px 5px;
    position: sticky;
    top: 0;
    background-color: #f5f5f5;
    z-index: 1;

    .file-select-all {
      width: 5%;
      height: 0.83em;
    }

    .file-name {
      width: calc(65% - 35px);
      padding-left: 35px;
    }

    .file-date {
      text-align: center;
      width: 20%;
    }

    .file-size {
      text-align: center;
      width: 10%;
    }
  }

  .file-item-container {
    display: flex;
    width: 100%;
    margin: 0;
    border-radius: 0;

    &:hover {
      background-color: rgb(0, 0, 0, 0.04);
    }
  }

  .file-item-container.file-selected {
    &:hover {
      background-color: var(--file-manager-primary-color) !important;
    }
  }

  .file-item {
    flex-direction: row;
    height: 13px;
    justify-content: unset;
    padding: 15px;
    padding-left: 33px;
    margin: 0;
    width: calc(70% - 30px);

    &:hover {
      background-color: unset;
    }

    .selection-checkbox {
      top: 12px;
    }

    .file-name {
      max-width: 285px;
    }
  }

  .modified-date {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    width: calc(20%);
  }

  .size {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    width: calc(10%);
  }
}

.empty-folder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}