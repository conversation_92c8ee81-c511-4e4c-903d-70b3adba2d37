import React, { useEffect, useState } from "react";
import {
  Form,
  FormProps,
  Input,
  Select,
  DatePicker,
  Modal,
  Row,
  Col,
  TabsProps,
  Tabs,
  Button,
  Upload,
} from "antd";
import { toast } from "react-toastify";
import {
  NS_NhanSuCreateOrUpdateType,
  NS_NhanSuType,
} from "@/types/nS_NhanSu/nS_NhanSu";
import * as extensions from "@/utils/extensions";
import nS_NhanSuService from "@/services/nS_NhanSu/nS_NhanSuService";
import GioiTinhConstant from "@/constants/QLNhanSu/GioiTinhConstant";
import dayjs from "dayjs";
import TrangThaiConstant from "@/constants/QLNhanSu/TrangThaiConstant";
import { Dropdown } from "@amcharts/amcharts5/.internal/charts/stock/toolbar/Dropdown";
import { DropdownOption } from "@/types/general";
import { duLieuDanhMucService } from "@/services/duLieuDanhMuc/duLieuDanhMuc.service";
import NhomDanhMucConstant from "@/constants/NhomDanhMucConstant";
import { departmentService } from "@/services/department/department.service";
import DepartmentOrganizationConstant from "@/constants/QLNhanSu/DepartmentOrganizationConstant";
import BangCapTable from "./table/BangCapTable";
import HopDongLaoDongTable from "./table/HopDongLaoDongTable";
import { PlusOutlined, UploadOutlined } from "@ant-design/icons";

interface Props {
  item?: NS_NhanSuType | null;
  onClose: () => void;
  onSuccess: () => void;
}

const NS_NhanSuCreateOrUpdate: React.FC<Props> = (props: Props) => {
  const [form] = Form.useForm<NS_NhanSuCreateOrUpdateType>();
  const [chucVuOptions, setChucVuOptions] = useState<DropdownOption[]>([]);
  const [phongBanOptions, setPhongBanOptions] = useState<DropdownOption[]>([]);
  const [chucVuLoaded, setChucVuLoaded] = useState(false);
  const [phongBanLoaded, setPhongBanLoaded] = useState(false);
  const [activeTab, setActiveTab] = React.useState<string>("1");

  const handleOnFinish: FormProps<NS_NhanSuCreateOrUpdateType>["onFinish"] =
    async (formData: NS_NhanSuCreateOrUpdateType) => {
      const payload = {
        ...formData,
      };
      if (props.item) {
        const response = await nS_NhanSuService.update(payload);
        if (response.status) {
          toast.success("Chỉnh sửa  thành công");
          form.resetFields();
          props.onSuccess();
          props.onClose();
        } else {
          toast.error(response.message);
        }
      } else {
        const response = await nS_NhanSuService.create(payload);
        if (response.status) {
          toast.success("Thêm mới  thành công");
          form.resetFields();
          props.onSuccess();
          props.onClose();
        } else {
          toast.error(response.message);
        }
      }
    };

  const handleCancel = () => {
    form.resetFields();
    props.onClose();
  };

  React.useEffect(() => {
    const fetchDropdownsChucVu = async () => {
      try {
        const response = await duLieuDanhMucService.GetDropdown(
          NhomDanhMucConstant.VaiTroDuAn
        );
        if (response.status) {
          setChucVuOptions(
            response.data.map((item: any) => ({
              value: item.value,
              label: item.label,
            }))
          );
          setChucVuLoaded(true);
        }
      } catch (error) {
        toast.error("Lỗi khi tải dữ liệu chức vụ");
      }
    };
    fetchDropdownsChucVu();
  }, []);

  React.useEffect(() => {
    const fetchDropdownsPhongBan = async () => {
      try {
        const response = await departmentService.getDropDepartmentByShortName(
          DepartmentOrganizationConstant.CongTyCoPhanHiNet
        );
        if (response.status) {
          setPhongBanOptions(
            response.data.map((item: any) => ({
              value: item.value,
              label: item.label,
            }))
          );
          setPhongBanLoaded(true);
        }
      } catch (error) {
        toast.error("Lỗi khi tải dữ liệu phòng ban");
      }
    };
    fetchDropdownsPhongBan();
  }, []);

  React.useEffect(() => {
    const fetchData = async () => {
      if (props.item && chucVuLoaded && phongBanLoaded) {
        const chucVu = await duLieuDanhMucService.GetById(
          props.item.chucVuId ?? ""
        );
        const phongBan = await departmentService.getDetail(
          props.item.phongBanId ?? ""
        );
        form.setFieldsValue({
          ...props.item,
          ngaySinh: props.item.ngaySinh ? dayjs(props.item.ngaySinh) : null,
          ngayCapCMND: props.item.ngayCapCMND
            ? dayjs(props.item.ngayCapCMND)
            : null,
          ngayVaoLam: props.item.ngayVaoLam
            ? dayjs(props.item.ngayVaoLam)
            : null,
        });
      }
    };
    fetchData();
  }, [form, props.item, chucVuLoaded, phongBanLoaded]);

  //Tab
  const tabItems: TabsProps["items"] = [
    {
      key: "1",
      label: "Thông tin chung của nhân sự",
      children: (
        <Form
          layout="vertical"
          form={form}
          name="formCreateUpdate"
          style={{ maxWidth: 1200 }}
          onFinish={handleOnFinish}
          autoComplete="off"
        >
          {props.item && (
            <Form.Item<NS_NhanSuCreateOrUpdateType> name="id" hidden>
              <Input />
            </Form.Item>
          )}
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Họ tên"
                name="hoTen"
                rules={[
                  { required: true, message: "Vui lòng nhập thông tin này!" },
                ]}
              >
                <Input placeholder="Họ tên" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Trạng thái"
                name="trangThai"
                rules={[
                  { required: true, message: "Vui lòng nhập thông tin này!" },
                ]}
              >
                <Select
                  placeholder="Chọn trạng thái làm việc"
                  options={[
                    {
                      value: TrangThaiConstant.DangLamViec,
                      label: TrangThaiConstant.getDisplayName(
                        TrangThaiConstant.DangLamViec
                      ),
                    },
                    {
                      value: TrangThaiConstant.NghiViec,
                      label: TrangThaiConstant.getDisplayName(
                        TrangThaiConstant.NghiViec
                      ),
                    },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Giới tính"
                name="gioiTinh"
                rules={[
                  { required: true, message: "Vui lòng nhập thông tin này!" },
                ]}
              >
                <Select
                  placeholder="Chọn giới tính"
                  options={[
                    {
                      value: GioiTinhConstant.Nam,
                      label: GioiTinhConstant.getDisplayName(
                        GioiTinhConstant.Nam
                      ),
                    },
                    {
                      value: GioiTinhConstant.Nu,
                      label: GioiTinhConstant.getDisplayName(
                        GioiTinhConstant.Nu
                      ),
                    },
                    {
                      value: GioiTinhConstant.Khac,
                      label: GioiTinhConstant.getDisplayName(
                        GioiTinhConstant.Khac
                      ),
                    },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Ngày sinh"
                name="ngaySinh"
                rules={[
                  { required: true, message: "Vui lòng nhập thông tin này!" },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const ngayVaoLam = getFieldValue("ngayVaoLam");
                      const ngayCapCMND = getFieldValue("ngayCapCMND");
                      if (!value) return Promise.resolve();
                      if (value.isAfter(dayjs(), "day")) {
                        return Promise.reject(
                          new Error("Bạn không đến từ tương lai!")
                        );
                      }
                      if (ngayVaoLam && value.isAfter(ngayVaoLam, "day")) {
                        return Promise.reject(
                          new Error("Ngày sinh phải trước ngày vào làm!")
                        );
                      }
                      if (ngayCapCMND && value.isAfter(ngayCapCMND, "day")) {
                        return Promise.reject(
                          new Error("Ngày sinh phải trước ngày cấp CMND!")
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <DatePicker
                  placeholder="Ngày sinh"
                  style={{ width: "100%" }}
                  format={{
                    format: "DD-MM-YYYY",
                    type: "mask",
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Chức vụ"
                name="chucVuId"
                rules={[
                  { required: true, message: "Vui lòng nhập thông tin này!" },
                ]}
              >
                <Select
                  placeholder="Chọn chức vụ"
                  options={chucVuOptions.map((opt) => ({
                    label: opt.label,
                    value: opt.value,
                  }))}
                  allowClear
                  showSearch
                  optionFilterProp="label"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Phòng ban"
                name="phongBanId"
                rules={[
                  { required: true, message: "Vui lòng nhập thông tin này!" },
                ]}
              >
                <Select
                  placeholder="Chọn phòng ban"
                  options={phongBanOptions.map((opt) => ({
                    label: opt.label,
                    value: opt.value,
                  }))}
                  allowClear
                  showSearch
                  optionFilterProp="label"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Ngày vào làm"
                name="ngayVaoLam"
                rules={[
                  { required: true, message: "Vui lòng nhập thông tin này!" },
                ]}
              >
                <DatePicker
                  placeholder="Ngày vào làm"
                  style={{ width: "100%" }}
                  format={{
                    format: "DD-MM-YYYY",
                    type: "mask",
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Điện thoại"
                name="dienThoai"
                rules={[
                  {
                    required: true,
                    message: "Vui lòng nhập thông tin này!",
                  },
                  {
                    pattern: /^[0-9]{10}$/,
                    message: "Số điện thoại phải có đúng 10 chữ số",
                  },
                ]}
              >
                <Input placeholder="Điện thoại" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Email"
                name="email"
              >
                <Input placeholder="Email" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType> label="CMND" name="cMND">
                <Input placeholder="CMND" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Ngày cấp CMND"
                name="ngayCapCMND"
              >
                <DatePicker
                  placeholder="Ngày cấp CMND"
                  style={{ width: "100%" }}
                  format={{
                    format: "DD-MM-YYYY",
                    type: "mask",
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Nơi cấp CMND"
                name="noiCapCMND"
              >
                <Input placeholder="Nơi cấp CMND" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Địa chỉ thường trú"
                name="diaChiThuongTru"
              >
                <Input placeholder="Địa chỉ thường trú" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Địa chỉ tạm trú"
                name="diaChiTamTru"
              >
                <Input placeholder="Địa chỉ tạm trú" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Mã số thuế cá nhân"
                name="maSoThueCaNhan"
              >
                <Input placeholder="Mã số thuế cá nhân" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Số tài khoản ngân hàng"
                name="soTaiKhoanNganHang"
              >
                <Input placeholder="Số tài khoản ngân hàng" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item<NS_NhanSuCreateOrUpdateType>
                label="Ngân hàng"
                name="nganHang"
              >
                <Input placeholder="Ngân hàng" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      ),
    },
    ...(props.item
      ? [
          {
            key: "2",
            label: "Bằng cấp",
            children: <BangCapTable nhanSuId={props.item?.id} />,
          },
          {
            key: "3",
            label: "Hợp đồng lao động",
            children: <HopDongLaoDongTable nhanSuId={props.item?.id} />,
          },
        ]
      : []),
  ];

  return (
    <Modal
      title={
        props.item != null
          ? "Chỉnh sửa thông tin nhân sự"
          : "Thêm mới thông tin nhân sự"
      }
      open={true}
      onOk={() => form.submit()}
      onCancel={handleCancel}
      cancelText="Đóng"
      width={1200}
      footer={
        activeTab === "1"
          ? [
              <Button key="cancel" onClick={handleCancel}>
                Đóng
              </Button>,
              <Button key="ok" type="primary" onClick={() => form.submit()}>
                Xác nhận
              </Button>,
            ]
          : [
              <Button key="cancel" onClick={handleCancel}>
                Đóng
              </Button>,
            ]
      }
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        tabPosition="top"
        destroyInactiveTabPane
      />
    </Modal>
  );
};
export default NS_NhanSuCreateOrUpdate;
