.ant-btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-dark-disabled,
.ant-btn-dark.disabled,
.ant-btn-dark[disabled],
.ant-btn-dark-disabled:hover,
.ant-btn-dark.disabled:hover,
.ant-btn-dark[disabled]:hover,
.ant-btn-dark-disabled:focus,
.ant-btn-dark.disabled:focus,
.ant-btn-dark[disabled]:focus,
.ant-btn-dark-disabled:active,
.ant-btn-dark.disabled:active,
.ant-btn-dark[disabled]:active,
.ant-btn-dark-disabled.active,
.ant-btn-dark.disabled.active,
.ant-btn-dark[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ant-btn-dark:hover,
.ant-btn-dark:focus {
  color: #fff !important;
  background-color: rgba(52, 58, 64, 0.75) !important;
  border-color: rgba(52, 58, 64, 0.1) !important;
}

.ant-btn-dark:active,
.ant-btn-dark.active {
  color: #fff !important;
  background-color: #1d2124 !important;
  border-color: #1d2124 !important;
}

.ant-btn-background-ghost.ant-btn-dark {
  color: #343a40;
  background: transparent;
  border-color: #343a40;
  text-shadow: none;
}

.ant-btn-background-ghost.ant-btn-dark:hover,
.ant-btn-background-ghost.ant-btn-dark:focus {
  color: rgba(52, 58, 64, 0.75) !important;
  background: transparent !important;
  border-color: rgba(52, 58, 64, 0.75) !important;
}

.ant-btn-background-ghost.ant-btn-dark:active,
.ant-btn-background-ghost.ant-btn-dark.active {
  color: #343a40 !important;
  background: transparent !important;
  border-color: #1d2124 !important;
}

//Danger
.ant-btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-outline-danger {
  color: #333;
  border-color: #dc3545;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-danger-disabled,
.ant-btn-danger.disabled,
.ant-btn-danger[disabled],
.ant-btn-danger-disabled:hover,
.ant-btn-danger.disabled:hover,
.ant-btn-danger[disabled]:hover,
.ant-btn-danger-disabled:focus,
.ant-btn-danger.disabled:focus,
.ant-btn-danger[disabled]:focus,
.ant-btn-danger-disabled:active,
.ant-btn-danger.disabled:active,
.ant-btn-danger[disabled]:active,
.ant-btn-danger-disabled.active,
.ant-btn-danger.disabled.active,
.ant-btn-danger[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #dc3545;
  border-color: #dc3545;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ant-btn-danger:hover,
.ant-btn-danger:focus,
.ant-btn-outline-danger:hover,
.ant-btn-outline-danger:focus {
  color: #fff !important;
  background-color: #c82333 !important;
  border-color: #bd2130 !important;
}

.ant-btn-danger:active,
.ant-btn-danger.active,
.ant-btn-outline-danger:active,
.ant-btn-outline-danger.active {
  color: #fff !important;
  background-color: #c82333 !important;
  border-color: #bd2130 !important;
}

.ant-btn-background-ghost.ant-btn-danger {
  color: #fff;
  background-color: #c82333 !important;
  border-color: #bd2130 !important;
  text-shadow: none;
}

.ant-btn-background-ghost.ant-btn-danger:hover,
.ant-btn-background-ghost.ant-btn-danger:focus {
  color: #fff;
  background-color: #c82333 !important;
  border-color: #bd2130 !important;
}

.ant-btn-background-ghost.ant-btn-danger:active,
.ant-btn-background-ghost.ant-btn-danger.active {
  color: #fff;
  background-color: #c82333 !important;
  border-color: #bd2130 !important;
}

//Primary
.ant-btn-primary {
  color: #fff;
  background-color: #0050c7;
  border-color: #0050c7;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-outline-primary {
  color: #333;
  border-color: #0050c7;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-primary-disabled,
.ant-btn-primary.disabled,
.ant-btn-primary[disabled],
.ant-btn-primary-disabled:hover,
.ant-btn-primary.disabled:hover,
.ant-btn-primary[disabled]:hover,
.ant-btn-primary-disabled:focus,
.ant-btn-primary.disabled:focus,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary-disabled:active,
.ant-btn-primary.disabled:active,
.ant-btn-primary[disabled]:active,
.ant-btn-primary-disabled.active,
.ant-btn-primary.disabled.active,
.ant-btn-primary[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #0050c7;
  border-color: #0050c7;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus,
.ant-btn-outline-primary:hover,
.ant-btn-outline-primary:focus {
  color: #fff !important;
  background-color: #0042a6 !important;
  border-color: #0042a6 !important;
}

.ant-btn-primary:active,
.ant-btn-primary.active,
.ant-btn-outline-primary:active,
.ant-btn-outline-primary.active {
  color: #fff !important;
  background-color: #0042a6 !important;
  border-color: #0042a6 !important;
}

.ant-btn-background-ghost.ant-btn-primary {
  color: #fff;
  background-color: #0042a6 !important;
  border-color: #0042a6 !important;
  text-shadow: none;
}

.ant-btn-background-ghost.ant-btn-primary:hover,
.ant-btn-background-ghost.ant-btn-primary:focus {
  color: #fff;
  background-color: #0042a6 !important;
  border-color: #0042a6 !important;
}

.ant-btn-background-ghost.ant-btn-primary:active,
.ant-btn-background-ghost.ant-btn-primary.active {
  color: #fff;
  background-color: #0042a6 !important;
  border-color: #0042a6 !important;
}

//download
.ant-btn-download {
  color: #fff;
  background-color: #8d00c7;
  border-color: #8d00c7;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-download-disabled,
.ant-btn-download.disabled,
.ant-btn-download[disabled],
.ant-btn-download-disabled:hover,
.ant-btn-download.disabled:hover,
.ant-btn-download[disabled]:hover,
.ant-btn-download-disabled:focus,
.ant-btn-download.disabled:focus,
.ant-btn-download[disabled]:focus,
.ant-btn-download-disabled:active,
.ant-btn-download.disabled:active,
.ant-btn-download[disabled]:active,
.ant-btn-download-disabled.active,
.ant-btn-download.disabled.active,
.ant-btn-download[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #8d00c7;
  border-color: #8d00c7;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ant-btn-download:hover,
.ant-btn-download:focus {
  color: #fff !important;
  background-color: #70009c !important;
  border-color: #70009c !important;
}

.ant-btn-download:active,
.ant-btn-download.active {
  color: #fff !important;
  background-color: #8d00c7 !important;
  border-color: #8d00c7 !important;
}

.ant-btn-background-ghost.ant-btn-download {
  color: #fff;
  background-color: #8d00c7 !important;
  border-color: #8d00c7 !important;
  text-shadow: none;
}

.ant-btn-background-ghost.ant-btn-download:hover,
.ant-btn-background-ghost.ant-btn-download:focus {
  color: #fff;
  background-color: #70009c !important;
  border-color: #70009c !important;
}

.ant-btn-background-ghost.ant-btn-download:active,
.ant-btn-background-ghost.ant-btn-download.active {
  color: #fff;
  background-color: #70009c !important;
  border-color: #70009c !important;
}

.ant-btn-lightdark {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-lightdark-disabled,
.ant-btn-lightdark.disabled,
.ant-btn-lightdark[disabled],
.ant-btn-lightdark-disabled:hover,
.ant-btn-lightdark.disabled:hover,
.ant-btn-lightdark[disabled]:hover,
.ant-btn-lightdark-disabled:focus,
.ant-btn-lightdark.disabled:focus,
.ant-btn-lightdark[disabled]:focus,
.ant-btn-lightdark-disabled:active,
.ant-btn-lightdark.disabled:active,
.ant-btn-lightdark[disabled]:active,
.ant-btn-lightdark-disabled.active,
.ant-btn-lightdark.disabled.active,
.ant-btn-lightdark[disabled].active {
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ant-btn-lightdark:hover,
.ant-btn-lightdark:focus {
  color: #fff !important;
  background-color: rgba(108, 117, 125, 0.75) !important;
  border-color: rgba(108, 117, 125, 0.1) !important;
}

.ant-btn-lightdark:active,
.ant-btn-lightdark.active {
  color: #fff !important;
  background-color: #545b62 !important;
  border-color: #545b62 !important;
}

.ant-btn-background-ghost.ant-btn-lightdark {
  color: #6c757d;
  background: transparent;
  border-color: #6c757d;
  text-shadow: none;
}

.ant-btn-background-ghost.ant-btn-lightdark:hover,
.ant-btn-background-ghost.ant-btn-lightdark:focus {
  color: rgba(108, 117, 125, 0.75) !important;
  background: transparent !important;
  border-color: rgba(108, 117, 125, 0.75) !important;
}

.ant-btn-background-ghost.ant-btn-lightdark:active,
.ant-btn-background-ghost.ant-btn-lightdark.active {
  color: #6c757d !important;
  background: transparent !important;
  border-color: #545b62 !important;
}

.ant-btn-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-outline-success {
  color: #333;
  border-color: #28a745;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-success-disabled,
.ant-btn-success.disabled,
.ant-btn-success[disabled],
.ant-btn-success-disabled:hover,
.ant-btn-success.disabled:hover,
.ant-btn-success[disabled]:hover,
.ant-btn-success-disabled:focus,
.ant-btn-success.disabled:focus,
.ant-btn-success[disabled]:focus,
.ant-btn-success-disabled:active,
.ant-btn-success.disabled:active,
.ant-btn-success[disabled]:active,
.ant-btn-success-disabled.active,
.ant-btn-success.disabled.active,
.ant-btn-success[disabled].active {
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ant-btn-success:hover,
.ant-btn-success:focus,
.ant-btn-outline-success:hover,
.ant-btn-outline-success:focus {
  color: #fff !important;
  background-color: rgba(40, 167, 69, 0.75) !important;
  border-color: rgba(40, 167, 69, 0.1) !important;
}

.ant-btn-success:active,
.ant-btn-success.active,
.ant-btn-outline-success:active,
.ant-btn-outline-success.active {
  color: #fff !important;
  background-color: #1e7e34 !important;
  border-color: #1e7e34 !important;
}

.ant-btn-background-ghost.ant-btn-success {
  color: #28a745;
  background: transparent;
  border-color: #28a745;
  text-shadow: none;
}

.ant-btn-background-ghost.ant-btn-success:hover,
.ant-btn-background-ghost.ant-btn-success:focus {
  color: rgba(40, 167, 69, 0.75) !important;
  background: transparent !important;
  border-color: rgba(40, 167, 69, 0.75) !important;
}

.ant-btn-background-ghost.ant-btn-success:active,
.ant-btn-background-ghost.ant-btn-success.active {
  color: #28a745 !important;
  background: transparent !important;
  border-color: #1e7e34 !important;
}

.ant-btn-warning {
  color: #fff;
  background-color: #eca52b;
  border-color: #eca52b;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-outline-warning {
  color: #333;
  border-color: #eca52b;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-warning-disabled,
.ant-btn-warning.disabled,
.ant-btn-warning[disabled],
.ant-btn-warning-disabled:hover,
.ant-btn-warning.disabled:hover,
.ant-btn-warning[disabled]:hover,
.ant-btn-warning-disabled:focus,
.ant-btn-warning.disabled:focus,
.ant-btn-warning[disabled]:focus,
.ant-btn-warning-disabled:active,
.ant-btn-warning.disabled:active,
.ant-btn-warning[disabled]:active,
.ant-btn-warning-disabled.active,
.ant-btn-warning.disabled.active,
.ant-btn-warning[disabled].active {
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ant-btn-warning:hover,
.ant-btn-warning:focus,
.ant-btn-outline-warning:hover,
.ant-btn-outline-warning:focus {
  color: #fff !important;
  background-color: rgba(236, 165, 43, 0.75) !important;
  border-color: rgba(236, 165, 43, 0.1) !important;
}

.ant-btn-warning:active,
.ant-btn-warning.active,
.ant-btn-outline-warning:active,
.ant-btn-outline-warning.active {
  color: #fff !important;
  background-color: #d18b13 !important;
  border-color: #d18b13 !important;
}

.ant-btn-background-ghost.ant-btn-warning {
  color: #eca52b;
  background: transparent;
  border-color: #eca52b;
  text-shadow: none;
}

.ant-btn-background-ghost.ant-btn-warning:hover,
.ant-btn-background-ghost.ant-btn-warning:focus {
  color: rgba(236, 165, 43, 0.75) !important;
  background: transparent !important;
  border-color: rgba(236, 165, 43, 0.75) !important;
}

.ant-btn-background-ghost.ant-btn-warning:active,
.ant-btn-background-ghost.ant-btn-warning.active {
  color: #eca52b !important;
  background: transparent !important;
  border-color: #d18b13 !important;
}

.ant-btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-outline-info {
  color: #333;
  border-color: #17a2b8;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-btn-info-disabled,
.ant-btn-info.disabled,
.ant-btn-info[disabled],
.ant-btn-info-disabled:hover,
.ant-btn-info.disabled:hover,
.ant-btn-info[disabled]:hover,
.ant-btn-info-disabled:focus,
.ant-btn-info.disabled:focus,
.ant-btn-info[disabled]:focus,
.ant-btn-info-disabled:active,
.ant-btn-info.disabled:active,
.ant-btn-info[disabled]:active,
.ant-btn-info-disabled.active,
.ant-btn-info.disabled.active,
.ant-btn-info[disabled].active {
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ant-btn-info:hover,
.ant-btn-info:focus,
.ant-btn-outline-info:hover,
.ant-btn-outline-info:focus {
  color: #fff !important;
  background-color: rgba(23, 162, 184, 0.75) !important;
  border-color: rgba(23, 162, 184, 0.1) !important;
}

.ant-btn-info:active,
.ant-btn-info.active,
.ant-btn-outline-info:active,
.ant-btn-outline-info.active {
  color: #fff !important;
  background-color: #117a8b !important;
  border-color: #117a8b !important;
}

.ant-btn-background-ghost.ant-btn-info {
  color: #17a2b8;
  background: transparent;
  border-color: #17a2b8;
  text-shadow: none;
}

.ant-btn-background-ghost.ant-btn-info:hover,
.ant-btn-background-ghost.ant-btn-info:focus {
  color: rgba(23, 162, 184, 0.75) !important;
  background: transparent !important;
  border-color: rgba(23, 162, 184, 0.75) !important;
}

.ant-btn-background-ghost.ant-btn-info:active,
.ant-btn-background-ghost.ant-btn-info.active {
  color: #17a2b8 !important;
  background: transparent !important;
  border-color: #117a8b !important;
}

.disable-animation:after {
  -webkit-animation: none !important;
  -moz-animation: none !important;
  -o-animation: none !important;
  -ms-animation: none !important;
  animation: none !important;
}
