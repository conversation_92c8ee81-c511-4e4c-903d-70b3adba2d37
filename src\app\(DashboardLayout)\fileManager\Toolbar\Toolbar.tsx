import React, { useState } from "react";
import { BsCopy, BsFolderPlus, BsGridFill, BsScissors } from "react-icons/bs";
import { FiRefreshCw } from "react-icons/fi";
import { MdClear, MdOutlineDelete, MdOutlineFileDownload, MdOutlineFileUpload } from "react-icons/md";
import { BiRename } from "react-icons/bi";
import { FaListUl, FaRegPaste } from "react-icons/fa6";
import LayoutToggler from "./LayoutToggler";
import "./Toolbar.scss";
import { useFileNavigation } from "@/contexts/FileNavigationContext";
import { useSelection } from "@/contexts/SelectionContext";
import { useClipBoard } from "@/contexts/ClipboardContext";
import { useLayout } from "@/contexts/LayoutContext";
import { validateApiCallback } from "@/utils/fileManagerUtils/validateApiCallback";
import { useTriggerAction } from "@/hooks/useTriggerAction";

// Định nghĩa kiểu cho các props của Toolbar
interface ToolbarProps {
  allowCreateFolder?: boolean;
  allowUploadFile?: boolean;
  onLayoutChange: (layout: string) => void;
  onRefresh: () => void;
  triggerAction: ReturnType<typeof useTriggerAction>;
}

const Toolbar: React.FC<ToolbarProps> = ({
  allowCreateFolder = true,
  allowUploadFile = true,
  onLayoutChange,
  onRefresh,
  triggerAction,
}) => {
  const [showToggleViewMenu, setShowToggleViewMenu] = useState(false);
  const { currentFolder } = useFileNavigation();
  const { selectedFiles, setSelectedFiles, handleDownload } = useSelection();
  const { clipBoard, setClipBoard, handleCutCopy, handlePasting } = useClipBoard();
  const { activeLayout } = useLayout();

  allowCreateFolder = currentFolder ? currentFolder?.permission?.create : true;
  allowUploadFile = currentFolder ? currentFolder?.permission?.upload : true;
  // Các item trong thanh công cụ
  const toolbarLeftItems = [
    {
      icon: <BsFolderPlus size={17} strokeWidth={0.3} />,
      text: "Thư mục mới",
      permission: allowCreateFolder,
      onClick: () => triggerAction.show("createFolder"),
    },
    {
      icon: <MdOutlineFileUpload size={18} />,
      text: "Tải lên",
      permission: allowUploadFile,
      onClick: () => triggerAction.show("uploadFile"),
    },
    {
      icon: <FaRegPaste size={18} />,
      text: "Dán",
      permission: !!clipBoard,
      onClick: handleFilePasting,
    },
  ];

  const toolbarRightItems = [
    {
      icon: activeLayout === "grid" ? <BsGridFill size={16} /> : <FaListUl size={16} />,
      title: "Chế độ xem",
      onClick: () => setShowToggleViewMenu((prev) => !prev),
    },
    {
      icon: <FiRefreshCw size={16} />,
      title: "Làm mới",
      onClick: () => {
        validateApiCallback(onRefresh, "onRefresh");
        setClipBoard(null);
      },
    },
  ];

  function handleFilePasting() {
    handlePasting(currentFolder);
  }

  const handleDownloadItems = () => {
    handleDownload();
    setSelectedFiles([]);
  };

  // Các hành động khi đã chọn file/folder
  if (selectedFiles.length > 0) {
    return (
      <div className="toolbar file-selected">
        <div className="file-action-container">
          <div>
            {selectedFiles.every(file => file.permission?.move) && (<button className="item-action file-action" onClick={() => handleCutCopy(true)}>
              <BsScissors size={18} />
              <span>Cắt</span>
            </button>
            )}

            {selectedFiles.every(file => file.permission?.copy) && (<button className="item-action file-action" onClick={() => handleCutCopy(false)}>
              <BsCopy strokeWidth={0.1} size={17} />
              <span>Sao chép</span>
            </button>)}

            {clipBoard?.files.length ?? (
              <button
                className="item-action file-action"
                onClick={handleFilePasting}
                disabled={!clipBoard}
              >
                <FaRegPaste size={18} />
                <span>Dán</span>
              </button>
            )}

            {selectedFiles.length === 1 && selectedFiles[0].permission?.rename && (
              <button
                className="item-action file-action"
                onClick={() => triggerAction.show("rename")}
              >
                <BiRename size={19} />
                <span>Đổi tên</span>
              </button>
            )}

            {selectedFiles.length >= 1
              && selectedFiles.every(file => file.permission?.download)
              && (
                <button className="item-action file-action" onClick={handleDownloadItems}>
                  <MdOutlineFileDownload size={19} />
                  <span>Tải xuống</span>
                </button>
              )}

            {selectedFiles.every(file => file.permission?.delete) && (<button
              className="item-action file-action"
              onClick={() => triggerAction.show("delete")}
            >
              <MdOutlineDelete size={19} />
              <span>Xóa</span>
            </button>)}


          </div>
          <button
            className="item-action file-action"
            title="Clear selection"
            onClick={() => setSelectedFiles([])}
          >
            <span>
              {selectedFiles.length} tệp tin được chọn
            </span>
            <MdClear size={18} />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="toolbar">
      <div className="fm-toolbar">
        <div>
          {toolbarLeftItems
            .filter((item) => item.permission)
            .map((item, index) => (
              <button className="item-action" key={index} onClick={item.onClick}>
                {item.icon}
                <span>{item.text}</span>
              </button>
            ))}
        </div>
        <div>
          {toolbarRightItems.map((item, index) => (
            <div key={index} className="toolbar-left-items">
              <button className="item-action icon-only" title={item.title} onClick={item.onClick}>
                {item.icon}
              </button>
              {index !== toolbarRightItems.length - 1 && <div className="item-separator"></div>}
            </div>
          ))}

          {showToggleViewMenu && (
            <LayoutToggler
              setShowToggleViewMenu={setShowToggleViewMenu}
              onLayoutChange={onLayoutChange}
            />
          )}
        </div>
      </div>
    </div >
  );
};

Toolbar.displayName = "Toolbar";

export default Toolbar;
