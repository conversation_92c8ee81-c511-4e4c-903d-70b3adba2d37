{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "checkJs": false, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "target": "ES2017"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/constants/ThemeConstant.ts", "src/libs/authentication.tx", "public/goJs/go.js", "src/app/(DashboardLayout)/fileManager/NavigationPane/NavigationPane.jsx", "src/app/(DashboardLayout)/fileManager/FileList/FileItem.jsx", "src/app/(DashboardLayout)/fileManager/FileList/FileItem.jsx", "src/app/(DashboardLayout)/fileManager/FileList/FileItem.jsx", "src/hooks/useDetectOutsideClicktjs", "src/app/(DashboardLayout)/fileManager/Actions/CreateFolder/CreateFolder.action.jsx", "src/app/(DashboardLayout)/DuAn/DetailKeHoachTrienKhaitsx"], "exclude": ["node_modules"]}