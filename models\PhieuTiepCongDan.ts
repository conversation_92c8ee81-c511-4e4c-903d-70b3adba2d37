const data = {
  modelName: 'PhieuTiepCongDan',
  label: 'Phiếu tiếp công dân',
  apiCreate: '/api/PhieuTiepCongDan/Create',
  apiRead: '/api/PhieuTiepCongDan/GetData',
  apiUpdate: '/api/PhieuTiepCongDan/Update',
  apiDelete: '/api/PhieuTiepCongDan/Delete',
  apiDetail: '/api/PhieuTiepCongDan/Detail',
  model: {
    id: {
      type: 'string | null',
      inputType: 'text',
      label: 'id',
      placeHolder: 'id',
      rules: `[{ required: true, message: 'Vui lòng nhập thông tin này!' }]`,
    },
    maBieuMau: {
      type: 'string | null',
      inputType: 'text',
      label: 'Mã biểu mẫu',
      placeHolder: 'Nhập mã biểu mẫu',
      rules: `[{ required: true, message: 'Vui lòng nhập thông tin này!' }]`,
    },
    tenBieuMau: {
      type: 'string | null',
      inputType: 'text',
      label: 'Tên biểu mẫu',
      placeHolder: 'Nhập tên biểu mẫu',
      rules: `[{ required: true, message: 'Vui lòng nhập thông tin này!' }]`,
    },
    duongDan: {
      type: 'string | null',
      inputType: 'text',
      label: 'Đường dẫn',
      placeHolder: 'Nhập tên đường dẫn',
      rules: `[{ required: true, message: 'Vui lòng nhập thông tin này!' }]`,
    },
  },
};
