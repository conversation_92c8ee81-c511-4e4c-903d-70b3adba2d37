.warningCard {
  display: flex;
  align-items: center;
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.warningCard:hover {
  transform: scale(1.02);
}

.warningCard i {
  font-size: 24px;
  color: red;
  margin-right: 15px;
}

.warningContent h3 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.warningContent p {
  margin: 5px 0 0;
  font-size: 14px;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}