@import "../../../../styles/variables";

.bread-crumb-container {
  position: relative;
  //  border-bottom: 1px solid $border-color;
  display: flex;

  .breadcrumb {
    width: 100%;
    height: 35px;
    min-height: 35px;
    max-height: 35px;
    display: flex;
    gap: 0.5rem;
    border-collapse: collapse;
    //border-bottom: 1px solid $border-color;
    padding: 6px 0 6PX 15px;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      height: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--file-manager-primary-color) !important;
    }

    .folder-name {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      font-weight: 500;
      min-width: fit-content;

      &:hover {
        cursor: pointer;
        color: var(--file-manager-primary-color);
      }
    }

    .hidden-folders {
      padding: 0 4px;
    }

    .folder-name-btn {
      background-color: transparent;
      border: none;
      padding: 0;

      &:hover,
      &:focus {
        cursor: pointer;
        color: var(--file-manager-primary-color);
        background-color: #dddcdc;
        border-radius: 5px;
      }
    }

  }
}

.hidden-folders-container {
  position: absolute;
  margin: 0;
  z-index: 2;
  background-color: rgb(99, 99, 99);
  color: white;
  padding: 4px;
  border-radius: 5px;
  font-size: .9em;
  left: 3rem;
  display: flex;
  flex-direction: column;
  gap: 5px;

  li {
    padding: 5px 10px;
    border-radius: 4px;

    &:hover {
      cursor: pointer;
      background-color: rgb(117, 117, 117);
    }
  }
}

.search-input {
  width: 30%;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-left: 1px solid $border-color;
  border-bottom: 1px solid $border-color;

  input {
    width: 100%;
    border: none;
    outline: none;
    font-size: 14px;
  }
}