import React, { useState, useEffect } from "react";
import { Checkbox } from "antd";
import styles from "./KeHoachTrienKhai.module.css";
import dayjs from "dayjs";
import { userService } from "@/services/user/user.service";
import { DA_KeHoachThucHienCreateOrUpdateType } from "@/types/dA_DuAn/dA_KeHoachThucHien";

interface RowType {
  key: string;
  stt: number | string;
  group?: string;
  isGroup?: boolean;
  ngayBatDau?: string | null;
  ngayKetThuc?: string | null;
  canhBaoTruocNgay?: number | null;
  isKeHoachNoiBo?: boolean | null;
  isCanhBao?: boolean | null;
  noiDungCongViec?: string | null;
  phanCong?: string | null;
}

interface Props {
  item: DA_KeHoachThucHienCreateOrUpdateType[] | null;
  idDuAn: string | null;
  iskeHoachNoiBo?: boolean | null;
  onClose?: () => void;
}

const KeHoachTrienKhaiDetailTable: React.FC<Props> = ({ item }) => {
  const [data, setData] = useState<RowType[]>([]);
  const [userOptions, setUserOptions] = useState<any[]>([]);

  useEffect(() => {
    userService.getDropdown().then((userRes) => {
      setUserOptions(Array.isArray(userRes.data) ? userRes.data : []);
    });
  }, []);

  useEffect(() => {
    let list: any[] = [];
    if (Array.isArray(item)) {
      list = item;
    } else if (item && Array.isArray((item as any).keHoachThucHienList)) {
      list = (item as any).keHoachThucHienList;
    }
    const groupMap: Record<string, string> = {};
    let groupIndex = 0;
    const groups = list
      .filter((row: any) => !row.groupNoiDungId)
      .map((row: any) => {
        const groupKey = row.id || `G${Date.now()}_${groupIndex}`;
        groupMap[row.id] = groupKey;
        groupIndex++;
        return {
          key: groupKey,
          stt: String.fromCharCode(65 + groupIndex - 1),
          group: groupKey,
          isGroup: true,
          ngayBatDau: row.ngayBatDau || null,
          ngayKetThuc: row.ngayKetThuc || null,
          canhBaoTruocNgay: row.canhBaoTruocNgay,
          isKeHoachNoiBo: row.isKeHoachNoiBo,
          isCanhBao: row.isCanhBao,
          noiDungCongViec: row.noiDungCongViec,
          phanCong: row.phanCong,
        };
      });
    const childs = list
      .filter((row: any) => row.groupNoiDungId)
      .map((row: any) => {
        const groupKey = groupMap[row.groupNoiDungId] || row.groupNoiDungId;
        return {
          key: row.id || `${row.stt}_${Math.random()}`,
          stt: row.stt,
          group: groupKey,
          isGroup: false,
          ngayBatDau: row.ngayBatDau || null,
          ngayKetThuc: row.ngayKetThuc || null,
          canhBaoTruocNgay: row.canhBaoTruocNgay,
          isKeHoachNoiBo: row.isKeHoachNoiBo,
          isCanhBao: row.isCanhBao,
          noiDungCongViec: row.noiDungCongViec,
          phanCong: row.phanCong,
        };
      });
    setData([...groups, ...childs]);
  }, [item]);

  const renderGroupRow = (row: RowType) => (
    <tr key={row.key} style={{ fontWeight: "bold", background: "#fafafa" }}>
      <td></td>
      <td style={{ textAlign: "center" }}>{row.stt}</td>
      <td>{row.noiDungCongViec}</td>
      <td colSpan={3}>
        {(() => {
          if (
            row.ngayBatDau &&
            row.ngayKetThuc &&
            dayjs(row.ngayBatDau, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid() &&
            dayjs(row.ngayKetThuc, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()
          ) {
            return `${dayjs(row.ngayBatDau, [
              "YYYY-MM-DD",
              "DD/MM/YYYY",
            ]).format("DD/MM/YYYY")} - ${dayjs(row.ngayKetThuc, [
              "YYYY-MM-DD",
              "DD/MM/YYYY",
            ]).format("DD/MM/YYYY")}`;
          }
          const childDates = data.filter(
            (r) => r.group === row.group && !r.isGroup
          );
          if (childDates.length === 0) return "-";
          const validStarts = childDates
            .map((r) => r.ngayBatDau)
            .filter(
              (d) => d && dayjs(d, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()
            )
            .map((d) => dayjs(d, ["YYYY-MM-DD", "DD/MM/YYYY"]));
          const validEnds = childDates
            .map((r) => r.ngayKetThuc)
            .filter(
              (d) => d && dayjs(d, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()
            )
            .map((d) => dayjs(d, ["YYYY-MM-DD", "DD/MM/YYYY"]));
          if (validStarts.length === 0 && validEnds.length === 0) return "-";
          const minStart =
            validStarts.length > 0
              ? dayjs(
                  validStarts.reduce((a, b) => (a.isBefore(b) ? a : b))
                ).format("DD/MM/YYYY")
              : "";
          const maxEnd =
            validEnds.length > 0
              ? dayjs(
                  validEnds.reduce((a, b) => (a.isAfter(b) ? a : b))
                ).format("DD/MM/YYYY")
              : "";
          return `${minStart}${minStart && maxEnd ? " - " : ""}${maxEnd}`;
        })()}
      </td>
    </tr>
  );

  const renderChildRow = (row: RowType) => (
    <tr key={row.key}>
      <td></td>
      <td style={{ textAlign: "center" }}>{row.stt}</td>
      <td>{row.noiDungCongViec}</td>
      <td>
        {row.ngayBatDau &&
        dayjs(row.ngayBatDau, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()
          ? dayjs(row.ngayBatDau, ["YYYY-MM-DD", "DD/MM/YYYY"]).format(
              "DD/MM/YYYY"
            )
          : ""}
        {row.ngayBatDau && row.ngayKetThuc ? " - " : ""}
        {row.ngayKetThuc &&
        dayjs(row.ngayKetThuc, ["YYYY-MM-DD", "DD/MM/YYYY"]).isValid()
          ? dayjs(row.ngayKetThuc, ["YYYY-MM-DD", "DD/MM/YYYY"]).format(
              "DD/MM/YYYY"
            )
          : ""}
      </td>
      <td>
        {userOptions.find((u: any) => u.value === row.phanCong)?.label || ""}
      </td>
      <td style={{ textAlign: "center" }}>
        {row.isCanhBao ? <Checkbox checked disabled /> : <Checkbox disabled />}
      </td>
    </tr>
  );

  return (
    <div style={{ background: "#fff", padding: 16 }}>
      <table className={styles.customTable}>
        <thead>
          <tr>
            <th style={{ width: 32 }}></th>
            <th style={{ width: 60 }}>STT</th>
            <th>Hạng mục công việc</th>
            <th>Thời gian thực hiện</th>
            <th>Assign</th>
            <th>Cảnh báo?</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row) =>
            row.isGroup ? renderGroupRow(row) : renderChildRow(row)
          )}
        </tbody>
      </table>
    </div>
  );
};

export default KeHoachTrienKhaiDetailTable;
