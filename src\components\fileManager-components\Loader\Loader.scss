.loader-container {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  height: -webkit-fill-available;
  width: -webkit-fill-available;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.spinner {
  font-size: 3rem;
  color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.upload-loading {
  position: relative;
  display: block;
  background-color: transparent;
  z-index: 0;

  .spinner {
    font-size: .9rem;
    color: black;
  }
}