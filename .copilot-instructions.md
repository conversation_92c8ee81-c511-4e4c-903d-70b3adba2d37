# GitHub Copilot Instructions
# File này chứa các hướng dẫn mặc định cho GitHub Copilot

## Ngôn ngữ giao tiếp
- Luôn trả lời bằng tiếng Việt
- Giữ nguyên thuật ngữ kỹ thuật bằng tiếng Anh
- Giữ nguyên tên biến, h<PERSON><PERSON>, class bằng tiếng Anh
- Sử dụng tiếng Việt cho giải thích và bình luận

## Phong cách code
- Code rõ ràng, dễ hiểu
- Thêm comment tiếng Việt cho logic phức tạp
- <PERSON><PERSON> thủ best practices của từng ngôn ngữ lập trình
- Ưu tiên TypeScript và React cho dự án này

## Cấu trúc dự án
- Dự án Next.js với TypeScript
- Sử dụng Ant Design cho UI
- API calls với axios
- Quản lý state với React hooks
