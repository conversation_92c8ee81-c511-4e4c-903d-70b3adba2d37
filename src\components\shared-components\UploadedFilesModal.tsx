import React from "react";
import { <PERSON><PERSON>, But<PERSON> } from "antd";
import { FileOutlined, DownloadOutlined } from "@ant-design/icons";
import { TaiLieuDinhKem } from "@/types/taiLieuDinhKem/taiLieuDinhKem";

interface UploadedFilesModalProps {
  open: boolean;
  onClose: () => void;
  files: TaiLieuDinhKem[];
  onDelete?: (fileId: string) => void;
  title?: string;
}

const UploadedFilesModal: React.FC<UploadedFilesModalProps> = ({
  open,
  onClose,
  files,
  onDelete,
  title = "Danh sách file đã tải lên",
}) => {
  const handleDownload = (file: TaiLieuDinhKem) => {
    if (file.duongDanFile) {
      window.open(file.duongDanFile, '_blank');
    }
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      title={title}
      width={600}
      bodyStyle={{ padding: 32, paddingTop: 0 }}
    >
      {files && files.length > 0 ? (
        <ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
          {files.map((file) => (
            <li
              key={file.id}
              style={{ display: "flex", alignItems: "center", fontSize: 15, padding: "7px 0", borderBottom: "1px solid #f0f0f0" }}
              title="Bấm để tải về"
            >
              <span style={{ flex: 1, display: "flex", alignItems: "center", gap: 7 }}>
                <FileOutlined style={{ color: "#52c41a", fontSize: 17 }} /> {file.tenTaiLieu}
              </span>
              <Button
                type="text"
                icon={<DownloadOutlined style={{ fontSize: 18 }} />}
                onClick={() => handleDownload(file)}
                style={{ color: "#1677ff" }}
                title="Tải xuống"
              />
            </li>
          ))}
        </ul>
      ) : (
        <div style={{ color: '#bfbfbf', textAlign: 'center', padding: '24px 0' }}>Chưa có file nào được tải lên.</div>
      )}
    </Modal>
  );
};

export default UploadedFilesModal;
