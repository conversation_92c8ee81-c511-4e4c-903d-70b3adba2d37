// components/SearchInput.tsx
import { useFileNavigation } from "@/contexts/FileNavigationContext";
import { useFiles } from "@/contexts/FilesContext";
import { FileManagerSearchType } from "@/types/fileManager/fileManager";
import sortFiles from "@/utils/fileManagerUtils/sortFiles";
import { useState } from "react";
import { MdSearch } from "react-icons/md";

const SearchInput = () => {
    const [query, setQuery] = useState("");
    const { currentFolder, setCurrentPathFiles, currentPath } = useFileNavigation();
    const { files } = useFiles();
    return (
        <div className="search-input">
            <MdSearch size={18} />
            <input
                type="text"
                placeholder={`Tìm kiếm trong ${currentFolder?.name ?? "thư mục"}...`}
                value={query}
                onChange={(e) => {
                    setQuery(e.target.value);
                }}
                onKeyDown={(e) => {
                    if (e.key === "Enter") {
                        setCurrentPathFiles(() => {
                            const currPathFiles = files.filter((file) => file.path?.startsWith(currentPath + "/")
                                && file.name.toLocaleLowerCase().includes(query?.toLocaleLowerCase()));
                            return sortFiles(currPathFiles);
                        });
                    }
                }}
            />
        </div>
    );
};

export default SearchInput;
