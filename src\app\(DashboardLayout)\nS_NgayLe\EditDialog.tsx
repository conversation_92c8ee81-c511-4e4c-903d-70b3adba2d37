import { useState, useEffect } from "react";
import { Button, Modal, Table, Input, DatePicker, Select } from "antd";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import nS_NgayLeService from "@/services/nS_NgayLe/nS_NgayLeService";
import { NS_NgayLeDto, NS_NgayLeCreateUpdateType } from "@/types/nS_NgayLe/NS_NgayLe";

const { Option } = Select;

interface EditDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: (values: NS_NgayLeCreateUpdateType) => void;
  item?: NS_NgayLeDto | null;
}

const EditDialog: React.FC<EditDialogProps> = ({ open, onClose, onSuccess, item }) => {
  const [editData, setEditData] = useState<Partial<NS_NgayLeDto>>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (item) {
      setEditData(item);
    } else {
      setEditData({
        tenNgayLe: "",
        ngayBatDau: "",
        ngayKetThuc: "",
        loai: 1,
        moTa: "",
        trangThai: "HoatDong",
        nam: new Date().getFullYear(),
      });
    }
  }, [item]);

  const handleEditField = (field: string, value: any) => {
    setEditData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const res = await nS_NgayLeService.createMany([{
        ...(item ? { id: item.id } : {}),
        tenNgayLe: editData.tenNgayLe || "",
        ngayBatDau: editData.ngayBatDau || "",
        ngayKetThuc: editData.ngayKetThuc || "",
        loai: editData.loai ?? 1,
        moTa: editData.moTa || "",
        trangThai: editData.trangThai || "HoatDong",
        nam: editData.nam ?? new Date().getFullYear(),
      }]);
      if (res.data?.status) {
        toast.success(item ? "Cập nhật thành công!" : "Thêm mới thành công!");
        onSuccess({
          ...(item ? { id: item.id } : {}),
          tenNgayLe: editData.tenNgayLe || "",
          ngayBatDau: editData.ngayBatDau || "",
          ngayKetThuc: editData.ngayKetThuc || "",
          loai: editData.loai ?? 1,
          moTa: editData.moTa || "",
          trangThai: editData.trangThai || "HoatDong",
          nam: editData.nam ?? new Date().getFullYear(),
        });
        onClose();
      } else {
        toast.error(res.data?.message || (item ? "Cập nhật thất bại" : "Thêm mới thất bại"));
      }
    } catch (err) {
      toast.error("Lỗi khi lưu dữ liệu");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal 
      open={open} 
      onCancel={onClose} 
      title={item ? "Chỉnh sửa ngày lễ" : "Thêm ngày lễ"} 
      footer={null} 
      width="1500px" 
      style={{ top: 200 }} 
      destroyOnClose
    >
      <Table
        dataSource={[editData]}
        rowKey="id"
        pagination={false}
        bordered
        size="small"
        columns={[
          { title: "Tên ngày lễ", dataIndex: "tenNgayLe", render: (val) => <Input value={val} onChange={e => handleEditField("tenNgayLe", e.target.value)} /> },
          { title: "Ngày bắt đầu", dataIndex: "ngayBatDau", render: (val) => <DatePicker value={val ? dayjs(val) : undefined} onChange={date => handleEditField("ngayBatDau", date ? date.format("YYYY-MM-DD") : "")} format="DD/MM/YYYY" style={{ width: "100%" }} /> },
          { title: "Ngày kết thúc", dataIndex: "ngayKetThuc", render: (val) => <DatePicker value={val ? dayjs(val) : undefined} onChange={date => handleEditField("ngayKetThuc", date ? date.format("YYYY-MM-DD") : "")} format="DD/MM/YYYY" style={{ width: "100%" }} /> },
          { title: "Loại", dataIndex: "loai", render: (val) => <Select value={val} onChange={v => handleEditField("loai", v)} style={{ width: 120 }}><Option value={1}>Quốc lễ</Option><Option value={2}>Công ty quy định</Option></Select> },
          { title: "Mô tả", dataIndex: "moTa", render: (val) => <Input value={val} onChange={e => handleEditField("moTa", e.target.value)} /> },
          { title: "Trạng thái", dataIndex: "trangThai", render: (val) => <Select value={val} onChange={v => handleEditField("trangThai", v)} style={{ width: 120 }}><Option value="HoatDong">Hoạt động</Option><Option value="KhongHoatDong">Không hoạt động</Option></Select> },
          { title: "Năm", dataIndex: "nam", render: (val) => <Input value={val} onChange={e => handleEditField("nam", e.target.value)} type="number" min={2000} max={2200} style={{ width: 80 }} /> },
        ]}
        loading={loading}
      />
      <div style={{ textAlign: "right", marginTop: 16 }}>
        <Button onClick={onClose} style={{ marginRight: 8 }}>Hủy</Button>
        <Button type="primary" onClick={handleSave} loading={loading}>{item ? "Cập nhật" : "Thêm mới"}</Button>
      </div>
    </Modal>
  );
};

export default EditDialog; 