const danT<PERSON><PERSON><PERSON><PERSON>am = [
  { ma: 1, dan_toc: 'Kinh' },
  { ma: 2, dan_toc: 'Tày' },
  { ma: 3, dan_toc: 'Thái' },
  { ma: 4, dan_toc: '<PERSON><PERSON>ờng' },
  { ma: 5, dan_toc: 'Khmer' },
  { ma: 6, dan_toc: 'Hoa' },
  { ma: 7, dan_toc: 'Nùng' },
  { ma: 8, dan_toc: "H'Mông" },
  { ma: 9, dan_toc: 'Dao' },
  { ma: 10, dan_toc: '<PERSON><PERSON>' },
  { ma: 11, dan_toc: 'Ngá<PERSON>' },
  { ma: 12, dan_toc: 'Ê Đê' },
  { ma: 13, dan_toc: 'Ba <PERSON>' },
  { ma: 14, dan_toc: '<PERSON><PERSON>' },
  { ma: 15, dan_toc: '<PERSON><PERSON>' },
  { ma: 16, dan_toc: '<PERSON><PERSON>' },
  { ma: 17, dan_toc: '<PERSON><PERSON>' },
  { ma: 18, dan_toc: '<PERSON><PERSON><PERSON>' },
  { ma: 19, dan_toc: 'Gi<PERSON><PERSON>' },
  { ma: 20, dan_toc: 'Chăm' },
  { ma: 21, dan_toc: 'Ê <PERSON>' },
  { ma: 22, dan_toc: 'Ba Na' },
  { ma: 23, dan_toc: 'X<PERSON> <PERSON><PERSON>ng' },
  { ma: 24, dan_toc: 'Sán <PERSON>y' },
  { ma: 25, dan_toc: 'C<PERSON> Ho' },
  { ma: 26, dan_toc: 'Ch<PERSON> Ro' },
  { ma: 27, dan_toc: 'Khơ Mú' },
  { ma: 28, dan_toc: 'Bru - Vân Kiều' },
  { ma: 29, dan_toc: 'Thổ' },
  { ma: 30, dan_toc: 'Gié - Triêng' },
  { ma: 31, dan_toc: 'Mạ' },
  { ma: 32, dan_toc: 'Co' },
  { ma: 33, dan_toc: 'Tà Ôi' },
  { ma: 34, dan_toc: 'Chứt' },
  { ma: 35, dan_toc: 'Rơ Măm' },
  { ma: 36, dan_toc: 'H’rê' },
  { ma: 37, dan_toc: 'Cống' },
  { ma: 38, dan_toc: 'Bố Y' },
  { ma: 39, dan_toc: 'La Hủ' },
  { ma: 40, dan_toc: 'La Ha' },
  { ma: 41, dan_toc: 'Phù Lá' },
  { ma: 42, dan_toc: 'La' },
  { ma: 43, dan_toc: 'Chứt' },
  { ma: 44, dan_toc: 'Mạ' },
  { ma: 45, dan_toc: 'Kháng' },
  { ma: 46, dan_toc: 'Xinh Mun' },
  { ma: 47, dan_toc: 'Hà Nhì' },
  { ma: 48, dan_toc: 'Cơ Lao' },
  { ma: 49, dan_toc: "B'Râu" },
  { ma: 50, dan_toc: 'Lô Lô' },
  { ma: 51, dan_toc: 'Mảng' },
  { ma: 52, dan_toc: 'Pà Thẻn' },
  { ma: 53, dan_toc: 'Ngái' },
  { ma: 54, dan_toc: 'Si La' },
];

export default danTocVietNam;
