import "./Button.scss";
import React, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KeyboardEventHandler } from "react";

type ButtonProps = {
  onClick?: MouseEventHandler<HTMLButtonElement>;
  onKeyDown?: KeyboardEventHandler<HTMLButtonElement>;
  type?: "primary" | "secondary" | "danger" | string;
  padding?: string;
  children: ReactNode;
};

const Button = ({
  onClick,
  onKeyDown,
  type = "primary",
  padding = "0.4rem 0.8rem",
  children,
}: ButtonProps) => {
  return (
    <button
      onClick={onClick}
      onKeyDown={onKeyDown}
      className={`fm-button fm-button-${type}`}
      style={{ padding }}
    >
      {children}
    </button>
  );
};

export default Button;