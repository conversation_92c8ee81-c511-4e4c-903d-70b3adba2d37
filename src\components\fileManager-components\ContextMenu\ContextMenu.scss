@import "../../../styles/variables";

.fm-context-menu {
  position: absolute;
  background-color: white;
  border: 1px solid #c6c6c6;
  border-radius: 6px;
  padding: 4px;
  z-index: 1;
  transition: opacity 0.1s linear;

  .file-context-menu-list {
    font-size: 1.1em;

    ul {
      list-style-type: none;
      padding-left: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 3px;

      li {
        display: flex;
        gap: 9px;
        align-items: center;
        padding: 3px 13px;
        position: relative;
        border-radius: 4px;

        &:hover {
          cursor: pointer;
          background-color: rgb(0, 0, 0, 0.07);
        }
      }

      li.active {
        background-color: rgb(0, 0, 0, 0.07);
      }

      li.disable-paste {
        opacity: 0.5;

        &:hover {
          cursor: default;
          background-color: transparent;
        }
      }
    }

    .divider {
      border-bottom: 1px solid #c6c6c6;
      margin: 5px 0 3px 0;
    }

    .list-expand-icon {
      margin-left: auto;
      color: #444444;
    }

    .sub-menu {
      width: 10rem;
      position: absolute;
      top: 0;
      background-color: white;
      border: 1px solid #c6c6c6;
      border-radius: 6px;
      padding: 4px;
      z-index: 1;

      .item-selected {
        width: 13px;
        color: #444444;
      }

      li {

        &:hover {
          background-color: rgb(0, 0, 0, 0.07) !important;
        }
      }
    }

    .sub-menu.right {
      left: calc(100% - 2px);
    }

    .sub-menu.left {
      left: calc(-83%);
    }
  }
}

.fm-context-menu.hidden {
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

.fm-context-menu.visible {
  opacity: 1;
  pointer-events: all;
  visibility: visible;
}