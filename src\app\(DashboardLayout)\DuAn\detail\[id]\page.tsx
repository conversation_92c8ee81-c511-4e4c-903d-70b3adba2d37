'use client';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import DA_DuAnDetail from '../../detail';
import dA_DuAnService from '@/services/dA_DuAn/dA_DuAnService';
import { DA_DuAnType } from '@/types/dA_DuAn/dA_DuAn';

export default function Page() {
  const params = useParams();
  const id = params?.id?.toString() ?? '';
  const [data, setData] = useState<DA_DuAnType | null>(null);

  useEffect(() => {
    if (id) {
      dA_DuAnService.get(id).then(res => {
        setData(res?.data ?? null);
      });
    }
  }, [id]);

  if (!data) return <div>Không tìm thấy dữ liệu</div>;
  return <DA_DuAnDetail item={data} itemId={id} />;
}
