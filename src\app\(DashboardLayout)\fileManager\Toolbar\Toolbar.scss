@import "../../../../styles/variables";

.toolbar {
  height: 45px;
  min-height: 45px;
  max-height: 45px;
  border-bottom: 1px solid $border-color;
  padding: 5px 10px;

  .file-action-container {
    display: flex;
    justify-content: space-between;

    >div {
      display: flex;
    }

    .file-action {
      background-color: transparent;
      gap: 5px;

      &:hover:not(:disabled) {
        cursor: pointer;
        background-color: rgb(0, 0, 0, 0.55) !important;
        border-radius: 3px;
        color: white;
        text-shadow: 0px 0px 1px white;
      }

      &:hover:disabled {
        cursor: default;
        background-color: transparent !important;
        color: #b0b0b0;
        text-shadow: none;
      }
    }
  }

  .fm-toolbar {
    display: flex;
    justify-content: space-between;

    >div {
      display: flex;
      position: relative;
    }

    .toolbar-left-items {
      display: flex;
    }

    .toggle-view {
      width: 10rem;
      position: absolute;
      z-index: 3;
      top: 105%;
      right: 22%;
      background-color: white;
      margin: 0;
      border: 1px solid #c4c4c4;
      border-radius: 5px;

      ul {
        list-style: none;
        padding-left: 0;
        margin: 0.4em 0;
        display: flex;
        flex-direction: column;
        gap: 1px;

        li {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 5px 20px 5px 10px;

          &:hover {
            cursor: pointer;
            background-color: rgba(0, 0, 0, 0.075);
          }

          span:nth-child(1) {
            width: 13px;
          }
        }
      }
    }
  }

  .item-action {
    background-color: white;
    display: flex;
    align-items: center;
    gap: 7px;
    padding: 8px 12px;
    font-size: 14px;
    width: fit-content;
    border: none;

    &:hover {
      cursor: pointer;
      background-color: rgb(0 0 0 / 12%) !important;
      border-radius: 3px;
    }

    .toggle-view-icon {
      background-color: transparent;
      border: none;

      &:hover {
        cursor: pointer;
      }
    }
  }

  .icon-only {
    padding: 0 8px !important;

    &:focus {
      background-color: rgb(0 0 0 / 12%);
      border-radius: 3px;
    }

    &:hover {
      color: var(--file-manager-primary-color);
    }
  }

  .item-separator {
    height: 36px;
    background: $border-color;
    width: 1px;
    margin: 0 5px;
  }
}

.file-selected {
  background-color: $item-hover-color;
}